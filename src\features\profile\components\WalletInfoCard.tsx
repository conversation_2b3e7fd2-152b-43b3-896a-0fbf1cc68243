import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/common/components/ui/card";
import { But<PERSON> } from "@/common/components/ui/button";
import { Wallet, ArrowDownCircle, ArrowUpCircle, Clock, Loader2 } from "lucide-react";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/common/components/ui/tabs";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/common/services/api";
import { toast } from "sonner";
import { Input } from "@/common/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/common/components/ui/dialog";

/**
 * WalletInfoCard component displays wallet balance and transaction history
 * 
 * @param {Object} props - Component props
 * @param {Object} props.backendUser - Backend user data
 * 
 * @status stable
 * @version 1.0.0
 */
const WalletInfoCard = ({ backendUser }: { backendUser: any }) => {
  const [activeTab, setActiveTab] = useState("balance");
  const [isDepositDialogOpen, setIsDepositDialogOpen] = useState(false);
  const [depositAmount, setDepositAmount] = useState("");
  const queryClient = useQueryClient();
  
  // Query for transactions
  const { data: transactions = [], isLoading } = useQuery({
    queryKey: ["transactions"],
    queryFn: () => apiService.getTransactions?.() || Promise.resolve([]),
    // If the getTransactions method hasn't been implemented yet, use mock data
    enabled: !!apiService.getTransactions,
  });

  // Mutation for creating a deposit transaction
  const { mutate: createDeposit, isPending: isDepositing } = useMutation({
    mutationFn: (amount: number) => 
      apiService.createTransaction({
        amount: amount,
        type: "deposit",
        reason: "User deposit"
      }),
    onSuccess: () => {
      toast.success("Deposit successful");
      setIsDepositDialogOpen(false);
      setDepositAmount("");
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });
    },
    onError: (error) => {
      toast.error(`Deposit failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

  // Fallback to mock transaction data if no real data is available
  const mockTransactions = [
    { id: 1, type: "deposit", amount: 100, date: "2025-05-01T10:30:00Z", status: "completed" },
    { id: 2, type: "challenge_entry", amount: -50, date: "2025-05-02T14:20:00Z", status: "completed" },
    { id: 3, type: "prize", amount: 200, date: "2025-05-03T09:15:00Z", status: "completed" },
    { id: 4, type: "withdrawal", amount: -100, date: "2025-05-04T16:45:00Z", status: "pending" },
  ];

  // Use real transactions if available, otherwise use mock data
  const displayTransactions = transactions.length > 0 ? transactions : mockTransactions;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "deposit":
        return <ArrowDownCircle className="h-4 w-4 text-green-500" />;
      case "withdrawal":
        return <ArrowUpCircle className="h-4 w-4 text-red-500" />;
      case "challenge_entry":
        return <ArrowUpCircle className="h-4 w-4 text-red-500" />;
      case "prize":
        return <ArrowDownCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-forex-neutral" />;
    }
  };

  const getTransactionLabel = (type: string) => {
    switch (type) {
      case "deposit":
        return "Deposit";
      case "withdrawal":
        return "Withdrawal";
      case "challenge_entry":
        return "Challenge Entry";
      case "prize":
        return "Prize Payout";
      default:
        return type;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleDeposit = () => {
    const amount = parseFloat(depositAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }
    createDeposit(amount);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Wallet</CardTitle>
          <CardDescription>Your balance and transaction history</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="balance" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="balance">Balance</TabsTrigger>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
            </TabsList>
            <TabsContent value="balance" className="space-y-4">
              <div className="mt-4 p-4 border border-gray-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium text-forex-neutral">Available Balance</h3>
                    <p className="text-2xl font-bold text-forex-dark">
                      ${backendUser?.walletCredit || "0.00"}
                    </p>
                  </div>
                  <Wallet className="h-8 w-8 text-forex-primary" />
                </div>
                <div className="mt-4 grid grid-cols-2 gap-2">
                  <Button size="sm" onClick={() => setIsDepositDialogOpen(true)}>Deposit</Button>
                  <Button variant="outline" size="sm">Withdraw</Button>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="transactions">
              <div className="mt-4">
                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="w-8 h-8 border-4 border-forex-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                    <p className="text-forex-neutral mt-2">Loading transactions...</p>
                  </div>
                ) : displayTransactions.length > 0 ? (
                  <div className="space-y-2">
                    {displayTransactions.map((transaction: any) => (
                      <div 
                        key={transaction.id} 
                        className="p-3 border border-gray-200 rounded-md flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-3">
                          {getTransactionIcon(transaction.type)}
                          <div>
                            <p className="font-medium text-forex-dark">{getTransactionLabel(transaction.type)}</p>
                            <p className="text-xs text-forex-neutral">{formatDate(transaction.date)}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <p className={`font-medium ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                          </p>
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(transaction.status)}`}>
                            {transaction.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-forex-neutral">No transactions found</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Deposit Dialog */}
      <Dialog open={isDepositDialogOpen} onOpenChange={setIsDepositDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Funds to Wallet</DialogTitle>
            <DialogDescription>
              Enter the amount you would like to deposit to your wallet.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="amount" className="text-sm font-medium text-forex-neutral">
                Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-forex-dark font-medium">$</span>
                <Input 
                  id="amount"
                  type="number"
                  min="0.01"
                  step="0.01"
                  placeholder="0.00"
                  className="pl-8"
                  value={depositAmount}
                  onChange={(e) => setDepositAmount(e.target.value)}
                  disabled={isDepositing}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDepositDialogOpen(false)} disabled={isDepositing}>
              Cancel
            </Button>
            <Button onClick={handleDeposit} disabled={isDepositing}>
              {isDepositing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Deposit Funds"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default WalletInfoCard;
