/**
 * cTrader Service
 * @description Service for interacting with the cTrader API
 * @version 1.0.0
 * @status stable
 */

import { apiService } from './api';

/**
 * cTrader account information
 */
export interface CTraderAccount {
  accountId: string;
  accountNumber: string;
  balance: number;
  broker: string;
  currency: string;
  equity: number;
  freeMargin: number;
  isLive: boolean;
  leverage: number;
  margin: number;
  marginLevel: number;
  name: string;
  type: string;
}

/**
 * cTrader connection status
 */
export enum CTraderConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  AUTHENTICATED = 'authenticated',
  TOKEN_EXPIRED = 'token_expired',
  ERROR = 'error',
}

/**
 * cTrader service for interacting with the cTrader API
 */
class CTraderService {
  private tokenCache: Map<number, { accessToken: string; refreshToken: string; timestamp: number }> = new Map();
  private readonly TOKEN_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get cTrader authorization URL
   * @returns Promise with authorization URL
   */
  async getAuthorizationUrl(): Promise<string> {
    try {
      const response = await apiService.getCTraderAuthUrl();
      return response.url;
    } catch (error) {
      console.error('Error getting cTrader authorization URL:', error);
      throw error;
    }
  }

  /**
   * Exchange authorization code for access token
   * @param code - Authorization code
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with success status
   */
  async exchangeCodeForToken(code: string, challengeEntryId: number): Promise<boolean> {
    try {
      await apiService.exchangeCTraderCode({
        code,
        challengeEntryId,
      });
      return true;
    } catch (error) {
      console.error('Error exchanging code for token:', error);
      throw error;
    }
  }

  /**
   * Connect to cTrader WebSocket
   * @param challengeEntryId - Challenge entry ID
   * @param accountId - Account ID
   * @returns Promise with success status
   */
  async connectWebSocket(challengeEntryId: number, accountId: string): Promise<boolean> {
    try {
      await apiService.connectCTrader({
        challengeEntryId,
        accountId,
      });
      return true;
    } catch (error) {
      console.error('Error connecting to cTrader WebSocket:', error);
      throw error;
    }
  }

  /**
   * Disconnect from cTrader WebSocket
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with success status
   */
  async disconnectWebSocket(challengeEntryId: number): Promise<boolean> {
    try {
      await apiService.disconnectCTrader(challengeEntryId);
      return true;
    } catch (error) {
      console.error('Error disconnecting from cTrader WebSocket:', error);
      throw error;
    }
  }

  /**
   * Refresh cTrader token
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with success status
   */
  async refreshToken(challengeEntryId: number): Promise<boolean> {
    try {
      await apiService.refreshCTraderToken(challengeEntryId);
      return true;
    } catch (error) {
      console.error('Error refreshing cTrader token:', error);
      throw error;
    }
  }

  /**
   * Get cTrader accounts
   * @param challengeEntryId - Challenge entry ID (optional)
   * @returns Promise with accounts
   */
  async getAccounts(challengeEntryId?: number): Promise<CTraderAccount[]> {
    try {
      if (!challengeEntryId) {
        console.error('No challenge entry ID provided');
        return [];
      }

      console.log(`Getting cTrader accounts for challenge entry ${challengeEntryId}`);

      // Use the API service to get accounts from the backend
      const accounts = await apiService.getCTraderAccounts(challengeEntryId);

      console.log(`Retrieved ${accounts.length} cTrader accounts`);
      return accounts;
    } catch (error) {
      console.error('Error getting cTrader accounts:', error);
      throw error;
    }
  }

  /**
   * Handle cTrader OAuth callback
   * @param url - Callback URL
   * @param challengeEntryId - Challenge entry ID
   * @returns Promise with tokens
   */
  async handleOAuthCallback(url: string, challengeEntryId: number): Promise<{ accessToken: string, refreshToken: string } | null> {
    try {
      console.log(`=== OAuth Callback Handler ===`);
      console.log(`Challenge Entry ID: ${challengeEntryId}`);
      console.log(`Callback URL: ${url}`);

      // Extract code from URL
      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      const error = urlObj.searchParams.get('error');

      if (error) {
        const errorDescription = urlObj.searchParams.get('error_description');
        throw new Error(`OAuth error: ${error}${errorDescription ? ` - ${errorDescription}` : ''}`);
      }

      if (!code) {
        throw new Error('No authorization code found in callback URL');
      }

      console.log(`Authorization code received: ${code.substring(0, 5)}...`);

      // Exchange code for token
      console.log('Exchanging code for tokens...');
      const response = await apiService.exchangeCTraderCode({
        code,
        challengeEntryId,
      });

      console.log('Code exchange successful, retrieving tokens from challenge entry...');

      // Retry mechanism for token retrieval
      let tokens = null;
      let retryCount = 0;
      const maxRetries = 3;

      while (!tokens && retryCount < maxRetries) {
        try {
          // Wait a bit for the database to be updated
          if (retryCount > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          const challengeEntry = await apiService.getChallengeEntryById(challengeEntryId);

          if (challengeEntry && challengeEntry.ctraderAccessToken && challengeEntry.ctraderRefreshToken) {
            tokens = {
              accessToken: challengeEntry.ctraderAccessToken,
              refreshToken: challengeEntry.ctraderRefreshToken
            };

            // Cache tokens for this challenge entry
            this.cacheTokens(challengeEntryId, tokens.accessToken, tokens.refreshToken);

            // Store tokens in localStorage for immediate use
            localStorage.setItem('ctrader_access_token', tokens.accessToken);
            localStorage.setItem('ctrader_refresh_token', tokens.refreshToken);
            localStorage.setItem(`ctrader_tokens_${challengeEntryId}`, JSON.stringify({
              accessToken: tokens.accessToken,
              refreshToken: tokens.refreshToken,
              timestamp: Date.now()
            }));

            console.log('OAuth callback handled successfully, tokens retrieved and cached');
            break;
          }
        } catch (error) {
          console.warn(`Token retrieval attempt ${retryCount + 1} failed:`, error);
        }

        retryCount++;
      }

      if (!tokens) {
        throw new Error('Failed to retrieve tokens after multiple attempts');
      }

      return tokens;
    } catch (error) {
      console.error('Error handling OAuth callback:', error);
      throw error;
    }
  }

  /**
   * Get connection status label
   * @param status - Connection status
   * @returns Human-readable status label
   */
  getConnectionStatusLabel(status: string): string {
    switch (status) {
      case CTraderConnectionStatus.DISCONNECTED:
        return 'Disconnected';
      case CTraderConnectionStatus.CONNECTING:
        return 'Connecting...';
      case CTraderConnectionStatus.CONNECTED:
        return 'Connected';
      case CTraderConnectionStatus.AUTHENTICATED:
        return 'Authenticated';
      case CTraderConnectionStatus.TOKEN_EXPIRED:
        return 'Token Expired';
      case CTraderConnectionStatus.ERROR:
        return 'Connection Error';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get connection status color
   * @param status - Connection status
   * @returns Status color
   */
  getConnectionStatusColor(status: string): string {
    switch (status) {
      case CTraderConnectionStatus.DISCONNECTED:
        return 'text-red-500';
      case CTraderConnectionStatus.CONNECTING:
        return 'text-yellow-500';
      case CTraderConnectionStatus.CONNECTED:
      case CTraderConnectionStatus.AUTHENTICATED:
        return 'text-green-500';
      case CTraderConnectionStatus.TOKEN_EXPIRED:
      case CTraderConnectionStatus.ERROR:
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  }

  /**
   * Cache tokens for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @param accessToken - Access token
   * @param refreshToken - Refresh token
   */
  private cacheTokens(challengeEntryId: number, accessToken: string, refreshToken: string): void {
    this.tokenCache.set(challengeEntryId, {
      accessToken,
      refreshToken,
      timestamp: Date.now()
    });
  }

  /**
   * Get cached tokens for a challenge entry
   * @param challengeEntryId - Challenge entry ID
   * @returns Cached tokens or null if not found/expired
   */
  private getCachedTokens(challengeEntryId: number): { accessToken: string, refreshToken: string } | null {
    const cached = this.tokenCache.get(challengeEntryId);
    if (!cached) return null;

    // Check if cache is still valid
    if (Date.now() - cached.timestamp > this.TOKEN_CACHE_DURATION) {
      this.tokenCache.delete(challengeEntryId);
      return null;
    }

    return {
      accessToken: cached.accessToken,
      refreshToken: cached.refreshToken
    };
  }

  /**
   * Get stored tokens (legacy method for backward compatibility)
   * @returns Access and refresh tokens
   */
  getTokens(): { accessToken: string, refreshToken: string } {
    const accessToken = localStorage.getItem('ctrader_access_token') || '';
    const refreshToken = localStorage.getItem('ctrader_refresh_token') || '';
    return { accessToken, refreshToken };
  }

  /**
   * Get tokens with multiple fallback mechanisms
   * @param challengeEntryId - Challenge entry ID (optional)
   * @returns Access and refresh tokens
   */
  async getTokensAdvanced(challengeEntryId?: number): Promise<{ accessToken: string, refreshToken: string }> {
    // Method 1: Try cached tokens for specific challenge entry
    if (challengeEntryId) {
      const cached = this.getCachedTokens(challengeEntryId);
      if (cached) {
        return cached;
      }

      // Method 2: Try localStorage for specific challenge entry
      try {
        const stored = localStorage.getItem(`ctrader_tokens_${challengeEntryId}`);
        if (stored) {
          const parsed = JSON.parse(stored);
          if (Date.now() - parsed.timestamp < this.TOKEN_CACHE_DURATION) {
            this.cacheTokens(challengeEntryId, parsed.accessToken, parsed.refreshToken);
            return {
              accessToken: parsed.accessToken,
              refreshToken: parsed.refreshToken
            };
          }
        }
      } catch (error) {
        // Silent error handling for token parsing
      }

      // Method 3: Try to fetch from backend
      try {
        const challengeEntry = await apiService.getChallengeEntryById(challengeEntryId);
        if (challengeEntry && challengeEntry.ctraderAccessToken && challengeEntry.ctraderRefreshToken) {
          const tokens = {
            accessToken: challengeEntry.ctraderAccessToken,
            refreshToken: challengeEntry.ctraderRefreshToken
          };
          this.cacheTokens(challengeEntryId, tokens.accessToken, tokens.refreshToken);
          return tokens;
        }
      } catch (error) {
        // Silent error handling for backend fetch
      }
    }

    // Method 4: Fallback to general localStorage
    const accessToken = localStorage.getItem('ctrader_access_token') || '';
    const refreshToken = localStorage.getItem('ctrader_refresh_token') || '';

    if (accessToken && refreshToken) {
      return { accessToken, refreshToken };
    }

    return { accessToken: '', refreshToken: '' };
  }

  /**
   * Get access token
   * @returns Access token
   */
  getAccessToken(): string | null {
    return localStorage.getItem('ctrader_access_token');
  }

  /**
   * Get refresh token
   * @returns Refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem('ctrader_refresh_token');
  }

  /**
   * Clear all cached tokens
   */
  clearTokenCache(): void {
    this.tokenCache.clear();
    localStorage.removeItem('ctrader_access_token');
    localStorage.removeItem('ctrader_refresh_token');

    // Clear challenge-specific tokens
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('ctrader_tokens_')) {
        localStorage.removeItem(key);
      }
    }
  }
}

// Export singleton instance
export const ctraderService = new CTraderService();
