import { useState, useEffect } from 'react';
import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { injected } from 'wagmi/connectors';

/**
 * Wallet connection hook that provides a simple interface
 * for wallet connection state and actions
 */
export const useWallet = () => {
  const { address, isConnected } = useAccount();
  const { connect, isPending: isConnecting } = useConnect();
  const { disconnect } = useDisconnect();
  const [error, setError] = useState<string | null>(null);

  /**
   * Connect to MetaMask wallet
   */
  const connectWallet = async () => {
    try {
      setError(null);
      connect({ connector: injected() });
    } catch (err) {
      console.error('Wallet connection error:', err);
      setError('Failed to connect wallet. Please make sure MetaMask is installed.');
    }
  };

  /**
   * Disconnect wallet
   */
  const disconnectWallet = () => {
    try {
      disconnect();
      setError(null);
    } catch (err) {
      console.error('Wallet disconnection error:', err);
      setError('Failed to disconnect wallet.');
    }
  };

  /**
   * Clear any existing errors when connection state changes
   */
  useEffect(() => {
    if (isConnected) {
      setError(null);
    }
  }, [isConnected]);

  return {
    // State
    address,
    isConnected,
    isConnecting,
    error,
    
    // Actions
    connect: connectWallet,
    disconnect: disconnectWallet,
    
    // Utilities
    getShortAddress: () => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    },
  };
};
