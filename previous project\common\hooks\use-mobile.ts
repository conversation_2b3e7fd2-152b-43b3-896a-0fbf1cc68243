import { useState, useEffect } from 'react';

/**
 * Breakpoint sizes matching Tailwind's default breakpoints
 */
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Type for screen size
 */
export type ScreenSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Hook to check if the screen is mobile (< 768px)
 * @returns {boolean} True if the screen is mobile
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Function to check if the screen is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < breakpoints.md);
    };

    // Initial check
    checkMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return isMobile;
}

/**
 * Hook to get the current screen size based on Tailwind breakpoints
 * @returns {ScreenSize} Current screen size
 */
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState<ScreenSize>('xs');

  useEffect(() => {
    // Function to determine screen size
    const checkScreenSize = () => {
      const width = window.innerWidth;

      if (width >= breakpoints['2xl']) {
        setScreenSize('2xl');
      } else if (width >= breakpoints.xl) {
        setScreenSize('xl');
      } else if (width >= breakpoints.lg) {
        setScreenSize('lg');
      } else if (width >= breakpoints.md) {
        setScreenSize('md');
      } else if (width >= breakpoints.sm) {
        setScreenSize('sm');
      } else {
        setScreenSize('xs');
      }
    };

    // Initial check
    checkScreenSize();

    // Add event listener for window resize with throttling
    let resizeTimer: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(checkScreenSize, 100);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimer);
    };
  }, []);

  return screenSize;
}

/**
 * Hook to check if the screen matches a specific breakpoint or range
 * @param {ScreenSize} min - Minimum screen size (inclusive)
 * @param {ScreenSize} [max] - Maximum screen size (inclusive)
 * @returns {boolean} True if the screen size is within the specified range
 */
export function useBreakpoint(min: ScreenSize, max?: ScreenSize) {
  const screenSize = useScreenSize();
  const breakpointOrder: ScreenSize[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];

  const minIndex = breakpointOrder.indexOf(min);
  const currentIndex = breakpointOrder.indexOf(screenSize);
  const maxIndex = max ? breakpointOrder.indexOf(max) : breakpointOrder.length - 1;

  return currentIndex >= minIndex && currentIndex <= maxIndex;
}
