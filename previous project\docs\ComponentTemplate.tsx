// 1. Imports - External libraries first, then internal components/utils
import React from 'react';
import { SomeExternalLibrary } from 'external-library';

import { SomeInternalComponent } from '@/common/components/ui';
import { useCustomHook } from '@/common/hooks';
import { cn } from '@/common/utils';

// 2. Type definitions and interfaces
/**
 * Props for the ExampleComponent
 * @interface ExampleComponentProps
 */
interface ExampleComponentProps {
  /** Primary content to display */
  content: string;
  /** Whether the component is in an active state */
  isActive?: boolean;
  /** Optional click handler */
  onClick?: () => void;
  /** Visual variant of the component */
  variant?: 'default' | 'primary' | 'secondary';
}

// 3. Component with JSDoc
/**
 * ExampleComponent - A template showing the recommended component structure
 * 
 * @component
 * @version 1.0.0
 * @status stable
 * 
 * @example
 * // Basic usage
 * <ExampleComponent content="Hello World" />
 * 
 * @example
 * // With active state and click handler
 * <ExampleComponent 
 *   content="Click me" 
 *   isActive={true}
 *   onClick={() => console.log('Clicked')}
 * />
 */
const ExampleComponent: React.FC<ExampleComponentProps> = ({
  content,
  isActive = false,
  onClick,
  variant = 'default'
}) => {
  // 4. State and hooks
  const [state, setState] = React.useState(false);
  const { data } = useCustomHook();

  // 5. Helper functions and handlers
  /**
   * Handles the click event
   */
  const handleClick = () => {
    setState(!state);
    if (onClick) onClick();
  };

  // 6. Computed values and classes
  const componentClasses = cn(
    'base-class',
    isActive && 'active-class',
    variant === 'primary' ? 'primary-class' : 'secondary-class'
  );

  // 7. Render
  return (
    <div className={componentClasses} onClick={handleClick}>
      <SomeInternalComponent />
      <div>{content}</div>
      {state && <div>Additional content when state is true</div>}
    </div>
  );
};

// 8. Export
export default ExampleComponent;
