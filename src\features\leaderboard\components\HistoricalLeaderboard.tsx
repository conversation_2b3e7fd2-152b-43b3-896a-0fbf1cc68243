/**
 * Historical Leaderboard Component
 * @description Component that displays historical leaderboard snapshots
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  Calendar,
  Clock,
  Filter,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Search,
  Calendar as CalendarIcon,
  ArrowUpDown,
  BarChart2
} from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '@/common/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Skeleton } from '@/common/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui/tabs';
import { Badge } from '@/common/components/ui/badge';
import { Input } from '@/common/components/ui/input';
import { Label } from '@/common/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/common/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/common/components/ui/popover';
import { Calendar as CalendarComponent } from '@/common/components/ui/calendar';
import { DetailedLeaderboardEntry } from '@/types/socketTypes';
import { leaderboardService, LeaderboardSnapshot } from '../services/leaderboardService';
import RealTimeLeaderboard from './RealTimeLeaderboard';

/**
 * HistoricalLeaderboard component props
 */
interface HistoricalLeaderboardProps {
  challengeId: number;
  className?: string;
}

/**
 * Component that displays historical leaderboard snapshots
 */
const HistoricalLeaderboard: React.FC<HistoricalLeaderboardProps> = ({
  challengeId,
  className = '',
}) => {
  const [snapshots, setSnapshots] = useState<LeaderboardSnapshot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSnapshot, setSelectedSnapshot] = useState<LeaderboardSnapshot | null>(null);
  const [comparisonSnapshot, setComparisonSnapshot] = useState<LeaderboardSnapshot | null>(null);
  const [showComparison, setShowComparison] = useState(false);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [snapshotType, setSnapshotType] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch snapshots on component mount and when filters change
  useEffect(() => {
    const fetchSnapshots = async () => {
      try {
        setLoading(true);
        setError(null);

        const options: any = {
          limit: 100, // Get a large number to filter client-side
        };

        if (dateRange.from) {
          options.startDate = dateRange.from;
        }

        if (dateRange.to) {
          options.endDate = dateRange.to;
        }

        if (snapshotType !== 'all') {
          options.snapshotType = snapshotType;
        }

        const data = await leaderboardService.getLeaderboardSnapshots(challengeId, options);
        setSnapshots(data);

        // Select the most recent snapshot by default if none is selected
        if (!selectedSnapshot && data.length > 0) {
          setSelectedSnapshot(data[0]);
        }
      } catch (error) {
        console.error('Error fetching snapshots:', error);
        setError('Failed to fetch snapshots. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchSnapshots();
  }, [challengeId, dateRange, snapshotType]);

  // Filter snapshots based on search query
  const filteredSnapshots = snapshots.filter(snapshot => {
    if (!searchQuery) return true;

    // Search by date or type
    return format(new Date(snapshot.snapshotTime), 'PPP').toLowerCase().includes(searchQuery.toLowerCase()) ||
           snapshot.snapshotType.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Paginate snapshots
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentSnapshots = filteredSnapshots.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredSnapshots.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Format snapshot type for display
  const formatSnapshotType = (type: string) => {
    switch (type) {
      case 'realtime':
        return 'Real-time';
      case 'daily':
        return 'Daily';
      case 'final':
        return 'Final';
      default:
        return type;
    }
  };

  // Get badge variant based on snapshot type
  const getSnapshotBadgeVariant = (type: string) => {
    switch (type) {
      case 'realtime':
        return 'outline';
      case 'daily':
        return 'secondary';
      case 'final':
        return 'default';
      default:
        return 'outline';
    }
  };

  // Handle snapshot selection
  const handleSnapshotSelect = (snapshot: LeaderboardSnapshot) => {
    setSelectedSnapshot(snapshot);
  };

  // Handle comparison selection
  const handleComparisonSelect = (snapshot: LeaderboardSnapshot) => {
    setComparisonSnapshot(snapshot);
    setShowComparison(true);
  };

  // Clear comparison
  const handleClearComparison = () => {
    setComparisonSnapshot(null);
    setShowComparison(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    setDateRange({ from: undefined, to: undefined });
    setSnapshotType('all');
    setSearchQuery('');
  };

  // Render loading state
  if (loading && snapshots.length === 0) {
    return (
      <Card className={`bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden ${className}`}>
        <CardHeader>
          <Skeleton className="h-6 w-48 bg-[#1a3a5f]" />
          <Skeleton className="h-4 w-64 bg-[#1a3a5f]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton key={index} className="h-16 w-full bg-[#1a3a5f]" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className={`bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden ${className}`}>
        <CardHeader>
          <CardTitle>Error Loading Snapshots</CardTitle>
          <CardDescription>
            {error}
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={() => window.location.reload()} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Filters */}
      <Card className="bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Snapshot Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Date Range Filter */}
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal bg-[#1a3a5f]/50 border-[#2a4d7d]/30"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#0c1b31] border-[#2a4d7d]/30" align="start">
                  <CalendarComponent
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={setDateRange}
                    numberOfMonths={2}
                    className="bg-[#0c1b31]"
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Snapshot Type Filter */}
            <div className="space-y-2">
              <Label>Snapshot Type</Label>
              <Select value={snapshotType} onValueChange={setSnapshotType}>
                <SelectTrigger className="bg-[#1a3a5f]/50 border-[#2a4d7d]/30">
                  <SelectValue placeholder="Select snapshot type" />
                </SelectTrigger>
                <SelectContent className="bg-[#0c1b31] border-[#2a4d7d]/30">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="realtime">Real-time</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="final">Final</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Search */}
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-white/50" />
                <Input
                  placeholder="Search snapshots..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 bg-[#1a3a5f]/50 border-[#2a4d7d]/30"
                />
              </div>
            </div>
          </div>

          {/* Reset Filters Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetFilters}
            className="mt-4"
          >
            Reset Filters
          </Button>
        </CardContent>
      </Card>

      {/* Snapshot List */}
      <Card className="bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Clock className="mr-2 h-5 w-5" />
            Leaderboard Snapshots
          </CardTitle>
          <CardDescription>
            {filteredSnapshots.length} snapshots found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentSnapshots.length === 0 ? (
            <div className="text-center py-8 text-white/70">
              No snapshots found. Try adjusting your filters.
            </div>
          ) : (
            <div className="space-y-4">
              {currentSnapshots.map((snapshot) => (
                <div
                  key={snapshot.id}
                  className={`p-4 rounded-lg border border-[#2a4d7d]/30 transition-all hover:bg-[#1a3a5f]/50 cursor-pointer ${
                    selectedSnapshot?.id === snapshot.id ? 'bg-[#1a3a5f]/70 border-forex-primary/50' : 'bg-[#1a3a5f]/20'
                  }`}
                  onClick={() => handleSnapshotSelect(snapshot)}
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-white">
                          {format(new Date(snapshot.snapshotTime), 'PPP')}
                        </span>
                        <Badge variant={getSnapshotBadgeVariant(snapshot.snapshotType)}>
                          {formatSnapshotType(snapshot.snapshotType)}
                        </Badge>
                      </div>
                      <div className="text-sm text-white/70 mt-1">
                        {format(new Date(snapshot.snapshotTime), 'p')} • {snapshot.entries.length} entries
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSnapshotSelect(snapshot);
                        }}
                        className={selectedSnapshot?.id === snapshot.id ? 'bg-forex-primary/20 text-forex-primary' : ''}
                      >
                        View
                      </Button>
                      {selectedSnapshot && selectedSnapshot.id !== snapshot.id && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleComparisonSelect(snapshot);
                          }}
                        >
                          Compare
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                {Array.from({ length: totalPages }).map((_, index) => (
                  <Button
                    key={index}
                    variant={currentPage === index + 1 ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handlePageChange(index + 1)}
                  >
                    {index + 1}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Snapshot View */}
      {selectedSnapshot && (
        <Tabs defaultValue="view" className="w-full">
          <TabsList className="bg-[#0a1f3d] border border-[#2a4d7d]/30 mb-4">
            <TabsTrigger value="view" className="data-[state=active]:bg-forex-primary/20 data-[state=active]:text-forex-primary">
              View Snapshot
            </TabsTrigger>
            {showComparison && (
              <TabsTrigger value="compare" className="data-[state=active]:bg-forex-primary/20 data-[state=active]:text-forex-primary">
                Compare Snapshots
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="view" className="mt-0">
            <Card className="bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden">
              <CardHeader>
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                  <div>
                    <CardTitle className="text-lg flex items-center">
                      <BarChart2 className="mr-2 h-5 w-5" />
                      Leaderboard Snapshot
                    </CardTitle>
                    <CardDescription>
                      {format(new Date(selectedSnapshot.snapshotTime), 'PPP p')} •
                      <Badge variant={getSnapshotBadgeVariant(selectedSnapshot.snapshotType)} className="ml-2">
                        {formatSnapshotType(selectedSnapshot.snapshotType)}
                      </Badge>
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full min-w-[650px] md:min-w-0">
                    <thead>
                      <tr className="bg-[#0a1f3d] text-left border-b border-[#2a4d7d]/30">
                        <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider w-[60px] md:w-auto">Rank</th>
                        <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider">Name</th>
                        <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Profit (%)</th>
                        <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right hidden md:table-cell">Trades</th>
                        <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Drawdown</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedSnapshot.entries.map((entry, index) => (
                        <tr
                          key={entry.userId}
                          className={`transition-all duration-300 ${
                            index === 0
                              ? "bg-gradient-to-r from-amber-500/10 to-transparent"
                              : index === 1
                                ? "bg-gradient-to-r from-slate-400/10 to-transparent"
                                : index === 2
                                  ? "bg-gradient-to-r from-amber-700/10 to-transparent"
                                  : "bg-[#0c1b31]/40"
                          } hover:bg-[#1a3a5f] border-b border-[#2a4d7d]/20`}
                        >
                          <td className="px-3 md:px-5 py-3">
                            <div className="flex items-center">
                              {index === 0 && <span className="text-amber-500 mr-1.5">🥇</span>}
                              {index === 1 && <span className="text-slate-400 mr-1.5">🥈</span>}
                              {index === 2 && <span className="text-amber-700 mr-1.5">🥉</span>}
                              <span className={`font-bold ${index < 3 ? 'text-white' : 'text-white/80'}`}>
                                {index + 1}
                              </span>
                            </div>
                          </td>
                          <td className="px-3 md:px-5 py-3">
                            <span className="font-medium text-white">{entry.username}</span>
                          </td>
                          <td className="px-3 md:px-5 py-3 text-right font-bold text-forex-profit">
                            {entry.score >= 0 ? '+' : ''}{entry.score.toFixed(2)}%
                          </td>
                          <td className="px-3 md:px-5 py-3 text-right hidden md:table-cell">
                            <span className="text-white/80 font-medium">{entry.tradeCount}</span>
                          </td>
                          <td className="px-3 md:px-5 py-3 text-right">
                            <span className="text-forex-loss font-medium">{entry.drawdown.toFixed(2)}%</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {showComparison && comparisonSnapshot && (
            <TabsContent value="compare" className="mt-0">
              <Card className="bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden mb-4">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                    <div>
                      <CardTitle className="text-lg flex items-center">
                        <ArrowUpDown className="mr-2 h-5 w-5" />
                        Snapshot Comparison
                      </CardTitle>
                      <CardDescription>
                        Comparing snapshots from {format(new Date(selectedSnapshot.snapshotTime), 'PPP')} and {format(new Date(comparisonSnapshot.snapshotTime), 'PPP')}
                      </CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={handleClearComparison}>
                      Clear Comparison
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-semibold text-white/70 mb-2 flex items-center">
                        <Badge variant={getSnapshotBadgeVariant(selectedSnapshot.snapshotType)} className="mr-2">
                          {formatSnapshotType(selectedSnapshot.snapshotType)}
                        </Badge>
                        {format(new Date(selectedSnapshot.snapshotTime), 'PPP p')}
                      </h3>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-[#0a1f3d] text-left border-b border-[#2a4d7d]/30">
                              <th className="px-3 py-2 text-xs font-semibold text-white/70 uppercase tracking-wider w-[50px]">Rank</th>
                              <th className="px-3 py-2 text-xs font-semibold text-white/70 uppercase tracking-wider">Name</th>
                              <th className="px-3 py-2 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Profit</th>
                            </tr>
                          </thead>
                          <tbody>
                            {selectedSnapshot.entries.slice(0, 10).map((entry, index) => (
                              <tr
                                key={entry.userId}
                                className="border-b border-[#2a4d7d]/20"
                              >
                                <td className="px-3 py-2">
                                  <span className="font-bold text-white/80">{index + 1}</span>
                                </td>
                                <td className="px-3 py-2">
                                  <span className="font-medium text-white">{entry.username}</span>
                                </td>
                                <td className="px-3 py-2 text-right font-bold text-forex-profit">
                                  {entry.score >= 0 ? '+' : ''}{entry.score.toFixed(2)}%
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-semibold text-white/70 mb-2 flex items-center">
                        <Badge variant={getSnapshotBadgeVariant(comparisonSnapshot.snapshotType)} className="mr-2">
                          {formatSnapshotType(comparisonSnapshot.snapshotType)}
                        </Badge>
                        {format(new Date(comparisonSnapshot.snapshotTime), 'PPP p')}
                      </h3>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-[#0a1f3d] text-left border-b border-[#2a4d7d]/30">
                              <th className="px-3 py-2 text-xs font-semibold text-white/70 uppercase tracking-wider w-[50px]">Rank</th>
                              <th className="px-3 py-2 text-xs font-semibold text-white/70 uppercase tracking-wider">Name</th>
                              <th className="px-3 py-2 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Profit</th>
                            </tr>
                          </thead>
                          <tbody>
                            {comparisonSnapshot.entries.slice(0, 10).map((entry, index) => (
                              <tr
                                key={entry.userId}
                                className="border-b border-[#2a4d7d]/20"
                              >
                                <td className="px-3 py-2">
                                  <span className="font-bold text-white/80">{index + 1}</span>
                                </td>
                                <td className="px-3 py-2">
                                  <span className="font-medium text-white">{entry.username}</span>
                                </td>
                                <td className="px-3 py-2 text-right font-bold text-forex-profit">
                                  {entry.score >= 0 ? '+' : ''}{entry.score.toFixed(2)}%
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      )}
    </div>
  );
};

export default HistoricalLeaderboard;
