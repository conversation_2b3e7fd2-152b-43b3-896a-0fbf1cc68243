import React, { useState } from "react";
import { User } from "@clerk/clerk-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/common/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/common/components/ui/avatar";
import { But<PERSON> } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Edit, Loader2 } from "lucide-react";
import { apiService } from "@/common/services/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

/**
 * PersonalInfoCard component displays user's personal information
 * 
 * Shows username, email, and profile picture
 * 
 * @param {Object} props - Component props
 * @param {User} props.user - Clerk user object
 * @param {Object} props.backendUser - Backend user data
 * 
 * @status stable
 * @version 1.0.0
 */
const PersonalInfoCard = ({ user, backendUser }: { user: User, backendUser: any }) => {
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [username, setUsername] = useState(user.username || backendUser?.username || "");
  const queryClient = useQueryClient();

  // Get user's initials for avatar fallback
  const getUserInitial = (): string => {
    return user?.firstName?.charAt(0) || user?.username?.charAt(0) || "U";
  };

  // Mutation for updating username
  const { mutate: updateUsername, isPending } = useMutation({
    mutationFn: (newUsername: string) => apiService.updateUsername(newUsername),
    onSuccess: () => {
      toast.success("Username updated successfully");
      setIsEditingUsername(false);
      // Invalidate the current user query to refetch updated data
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to update username: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

  const handleSaveUsername = () => {
    if (username.trim()) {
      updateUsername(username.trim());
    } else {
      toast.error("Please enter a valid username");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>Your account details and profile</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-start space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={user.imageUrl} alt={user.username || "User"} />
            <AvatarFallback className="bg-forex-primary text-white text-lg">
              {getUserInitial()}
            </AvatarFallback>
          </Avatar>
          <div className="space-y-4 flex-1">
            <div>
              <div className="flex items-center justify-between mb-1">
                <h3 className="text-sm font-medium text-forex-neutral">Username</h3>
                {!isEditingUsername && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() => setIsEditingUsername(true)}
                  >
                    <Edit className="h-3.5 w-3.5 mr-1" />
                    Edit
                  </Button>
                )}
              </div>
              {isEditingUsername ? (
                <div className="space-y-2">
                  <Input
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Enter username"
                    className="w-full"
                    disabled={isPending}
                  />
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      onClick={handleSaveUsername}
                      disabled={isPending}
                    >
                      {isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving
                        </>
                      ) : (
                        "Save"
                      )}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => {
                        setUsername(user.username || backendUser?.username || "");
                        setIsEditingUsername(false);
                      }}
                      disabled={isPending}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-forex-dark font-medium">{user.username || backendUser?.username}</p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-forex-neutral">Email</h3>
              <p className="text-forex-dark font-medium">{user.primaryEmailAddress?.emailAddress}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-forex-neutral">Member Since</h3>
              <p className="text-forex-dark font-medium">
                {new Date(user.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonalInfoCard;
