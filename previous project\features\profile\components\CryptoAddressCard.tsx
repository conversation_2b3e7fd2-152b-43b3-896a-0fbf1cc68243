import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/common/components/ui/card";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Bitcoin, Copy, Check, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { apiService } from "@/common/services/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

/**
 * CryptoAddressCard component for managing crypto wallet address
 * 
 * Allows users to view and update their crypto wallet address for prize payouts
 * 
 * @param {Object} props - Component props
 * @param {Object} props.backendUser - Backend user data
 * 
 * @status stable
 * @version 1.0.0
 */
const CryptoAddressCard = ({ backendUser }: { backendUser: any }) => {
  const [cryptoAddress, setCryptoAddress] = useState(backendUser?.cryptoAddress || "");
  const [isEditing, setIsEditing] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const queryClient = useQueryClient();

  // Mutation for updating crypto address
  const { mutate: updateCryptoAddress, isPending } = useMutation({
    mutationFn: (address: string) => apiService.updateCryptoAddress(address),
    onSuccess: () => {
      toast.success("Crypto address updated successfully");
      setIsEditing(false);
      // Invalidate the current user query to refetch updated data
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to update crypto address: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

  const handleSave = () => {
    if (cryptoAddress.trim()) {
      updateCryptoAddress(cryptoAddress.trim());
    } else {
      toast.error("Please enter a valid crypto address");
    }
  };

  const handleCopy = () => {
    if (cryptoAddress) {
      navigator.clipboard.writeText(cryptoAddress);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
      toast.success("Crypto address copied to clipboard");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Crypto Address</CardTitle>
        <CardDescription>Manage your crypto wallet address for prize payouts</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 border border-gray-200 rounded-md">
          <div className="flex items-start space-x-3">
            <Bitcoin className="h-5 w-5 text-forex-primary mt-0.5" />
            <div className="flex-1">
              <h3 className="font-medium text-forex-dark">Wallet Address</h3>
              <p className="text-sm text-forex-neutral mb-3">
                This address will be used for prize payouts and withdrawals
              </p>
              
              {isEditing ? (
                <div className="space-y-3">
                  <Input
                    value={cryptoAddress}
                    onChange={(e) => setCryptoAddress(e.target.value)}
                    placeholder="Enter your crypto wallet address"
                    className="w-full"
                    disabled={isPending}
                  />
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      onClick={handleSave}
                      disabled={isPending}
                    >
                      {isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving
                        </>
                      ) : (
                        "Save"
                      )}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => {
                        setCryptoAddress(backendUser?.cryptoAddress || "");
                        setIsEditing(false);
                      }}
                      disabled={isPending}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  {cryptoAddress ? (
                    <div className="flex items-center space-x-2">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm flex-1 overflow-x-auto">
                        {cryptoAddress}
                      </code>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={handleCopy}
                        className="h-8 w-8 p-0"
                      >
                        {isCopied ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  ) : (
                    <p className="text-forex-neutral italic">No crypto address set</p>
                  )}
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setIsEditing(true)}
                    className="mt-3"
                  >
                    {cryptoAddress ? "Update Address" : "Add Address"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CryptoAddressCard;
