# Layout Components

This directory contains layout components that are used across the application to maintain consistent structure and appearance.

## Components

### Logo

The `Logo` component displays the TradeChampionX logo with optional text. It can be configured with different sizes, color variants, and can be wrapped in a Link component for navigation.

**Props:**
- `variant`: Color variant of the logo ('default' | 'white')
- `showText`: Whether to show the text "TradeChampionX" next to the logo
- `size`: Size of the logo ('small' | 'medium' | 'large')
- `linkWrapper`: Whether to wrap the logo in a Link component that navigates to the home page

**Usage:**
```tsx
// Basic usage
<Logo />

// White variant without text
<Logo variant="white" showText={false} />

// Large size without link wrapper
<Logo size="large" linkWrapper={false} />
```

### Header

The `Header` component displays the main navigation header of the application, including the logo, navigation links, and authentication controls.

### Footer

The `Footer` component displays the footer section of the application, including links to various pages, social media, and copyright information.

### ProtectedRoute

The `ProtectedRoute` component is used to protect routes that require authentication. It redirects unauthenticated users to the login page.

### VerificationLoading

The `VerificationLoading` component displays a loading indicator while verifying user authentication.

### VerificationNotFound

The `VerificationNotFound` component displays an error message when user verification fails.

## Usage Guidelines

- Use the `Header` and `Footer` components on all pages to maintain consistent navigation and branding
- Use the `Logo` component whenever you need to display the TradeChampionX logo
- Use the `ProtectedRoute` component to wrap routes that require authentication
- Use the `VerificationLoading` and `VerificationNotFound` components during the authentication flow
