import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  ChevronRight,
  BarChart2,
  Clock,
  Calendar,
  Brain,
  Zap,
  PieChart,
  FileText,
  Image as ImageIcon,
  Tag,
  Smile,
  Frown,
  Meh,
  ArrowUpRight,
  ArrowDownRight,
  <PERSON>rkles
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/common/components/ui/button';
import { Badge } from '@/common/components/ui/badge';
import { <PERSON> } from 'react-router-dom';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/common/components/ui/tooltip';
import { cn } from '@/common/utils';

// Sample metrics data for visualization
const sampleMetrics = {
  winRate: 62.5,
  profitFactor: 2.3,
  averageWin: 1.8,
  averageLoss: 0.7,
  maxDrawdown: 4.2,
  sharpeRatio: 1.7,
  tradingDays: 14,
  totalTrades: 48,
  symbols: [
    { name: 'EURUSD', trades: 18, winRate: 67, pnl: 3.2 },
    { name: 'GBPUSD', trades: 12, winRate: 58, pnl: 1.8 },
    { name: 'USDJPY', trades: 10, winRate: 70, pnl: 2.5 },
    { name: 'AUDUSD', trades: 8, winRate: 50, pnl: 0.5 }
  ],
  timeOfDay: [
    { time: 'Morning', trades: 15, winRate: 73 },
    { time: 'Afternoon', trades: 20, winRate: 55 },
    { time: 'Evening', trades: 13, winRate: 62 }
  ],
  psychologicalInsights: [
    { type: 'Overtrading', severity: 'medium', description: 'Tendency to trade too frequently during volatile sessions' },
    { type: 'Early Exit', severity: 'low', description: 'Closing profitable trades too early' }
  ]
};

// Sample journal entries for showcase
const sampleJournalEntries = [
  {
    id: '1',
    title: 'Strong EURUSD Breakout Trade',
    date: '2023-10-15',
    content: 'Caught a clean breakout above resistance with strong momentum...',
    tags: ['Breakout', 'EURUSD', 'Success'],
    mood: 'positive',
    dayProfitability: 'profitable',
    pnlAmount: 2.3,
    hasImages: true
  },
  {
    id: '2',
    title: 'Market Analysis: NFP Impact',
    date: '2023-10-08',
    content: 'Today\'s Non-Farm Payroll data caused significant volatility...',
    tags: ['Analysis', 'Fundamental', 'NFP'],
    mood: 'neutral',
    dayProfitability: 'break-even',
    pnlAmount: 0.1,
    hasImages: false
  },
  {
    id: '3',
    title: 'Reflection on Risk Management',
    date: '2023-10-01',
    content: 'Need to improve position sizing on trending markets...',
    tags: ['Risk', 'Improvement', 'Reflection'],
    mood: 'negative',
    dayProfitability: 'unprofitable',
    pnlAmount: -1.2,
    hasImages: true
  }
];

const FeatureShowcase: React.FC = () => {
  const [activeTab, setActiveTab] = useState('analytics');
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#050c16] via-[#071426] to-[#050c16] z-0"></div>
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-forex-primary/30 to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-forex-primary/30 to-transparent"></div>

      {/* Animated background particles */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-forex-primary/20 rounded-full filter blur-3xl animate-blob"></div>
        <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-forex-accent/20 rounded-full filter blur-3xl animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 right-1/3 w-60 h-60 bg-blue-500/20 rounded-full filter blur-3xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-2xl mx-auto mb-16" data-aos="fade-up">
          {/* New feature badge */}
          <div className="inline-flex items-center px-5 py-2 bg-forex-primary/20 text-forex-primary font-medium rounded-full mb-6 border border-forex-primary/30 shadow-lg shadow-forex-primary/10 animate-fade-in">
            <Sparkles className="w-4 h-4 mr-2 animate-pulse" />
            <span>New Features</span>
          </div>

          {/* Heading */}
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-6 animate-fade-in leading-tight">
            Elevate Your Trading with <span className="bg-gradient-to-r from-forex-primary to-forex-accent bg-clip-text text-transparent">Advanced Tools</span>
          </h2>

          <p className="text-gray-300 text-lg mb-8 animate-fade-in">
            Gain deeper insights into your trading performance and maintain a comprehensive trading journal
          </p>
        </div>

        {/* Feature tabs */}
        <div className="max-w-5xl mx-auto" data-aos="zoom-in" data-aos-delay="200">
          <Tabs defaultValue="analytics" onValueChange={setActiveTab} className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList className="bg-[#0c1b31]/80 border border-[#2a4d7d]/30 p-1">
                <TabsTrigger
                  value="analytics"
                  className={`px-6 py-2.5 ${activeTab === 'analytics' ? 'bg-forex-primary text-white' : 'text-white/70 hover:text-white'}`}
                >
                  <BarChart2 className="w-4 h-4 mr-2" />
                  Advanced Analytics
                </TabsTrigger>
                <TabsTrigger
                  value="journal"
                  className={`px-6 py-2.5 ${activeTab === 'journal' ? 'bg-forex-primary text-white' : 'text-white/70 hover:text-white'}`}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Trading Journal
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Analytics Tab Content */}
            <TabsContent value="analytics" className="mt-0">
              <div className="bg-[#0c1b31]/80 backdrop-blur-md rounded-xl border border-[#2a4d7d]/30 overflow-hidden shadow-xl transform transition-all hover:shadow-glow-primary">
                {/* Header */}
                <div className="flex items-center justify-between bg-gradient-to-r from-[#0c2d5a] to-[#164279] px-6 py-4">
                  <div className="flex items-center">
                    <BarChart2 className="w-5 h-5 text-forex-primary mr-2.5" />
                    <h3 className="font-bold text-white text-lg">Advanced Performance Analytics</h3>
                  </div>
                  <Badge className="bg-forex-primary/20 text-forex-primary border border-forex-primary/30">
                    Real-time Updates
                  </Badge>
                </div>

                {/* Analytics Dashboard Preview */}
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    {/* Win Rate Card */}
                    <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30 hover:border-forex-primary/50 transition-all duration-300">
                      <div className="flex justify-between items-start mb-2">
                        <div className="text-gray-400 text-sm">Win Rate</div>
                        <div className="bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full flex items-center">
                          <ArrowUpRight className="w-3 h-3 mr-1" />
                          <span>+5.2%</span>
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-white mb-1">{sampleMetrics.winRate}%</div>
                      <div className="w-full bg-[#0c1b31] rounded-full h-2 mb-2">
                        <div className="bg-gradient-to-r from-blue-500 to-forex-primary h-2 rounded-full" style={{ width: `${sampleMetrics.winRate}%` }}></div>
                      </div>
                      <div className="text-xs text-gray-400">Benchmark: 50%</div>
                    </div>

                    {/* Profit Factor Card */}
                    <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30 hover:border-forex-primary/50 transition-all duration-300">
                      <div className="flex justify-between items-start mb-2">
                        <div className="text-gray-400 text-sm">Profit Factor</div>
                        <div className="bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full flex items-center">
                          <ArrowUpRight className="w-3 h-3 mr-1" />
                          <span>+0.3</span>
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-white mb-1">{sampleMetrics.profitFactor}</div>
                      <div className="w-full bg-[#0c1b31] rounded-full h-2 mb-2">
                        <div className="bg-gradient-to-r from-blue-500 to-forex-primary h-2 rounded-full" style={{ width: `${Math.min(sampleMetrics.profitFactor / 3 * 100, 100)}%` }}></div>
                      </div>
                      <div className="text-xs text-gray-400">Benchmark: 1.5</div>
                    </div>

                    {/* Sharpe Ratio Card */}
                    <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30 hover:border-forex-primary/50 transition-all duration-300">
                      <div className="flex justify-between items-start mb-2">
                        <div className="text-gray-400 text-sm">Sharpe Ratio</div>
                        <div className="bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full flex items-center">
                          <ArrowUpRight className="w-3 h-3 mr-1" />
                          <span>+0.2</span>
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-white mb-1">{sampleMetrics.sharpeRatio}</div>
                      <div className="w-full bg-[#0c1b31] rounded-full h-2 mb-2">
                        <div className="bg-gradient-to-r from-blue-500 to-forex-primary h-2 rounded-full" style={{ width: `${Math.min(sampleMetrics.sharpeRatio / 2 * 100, 100)}%` }}></div>
                      </div>
                      <div className="text-xs text-gray-400">Benchmark: 1.0</div>
                    </div>
                  </div>

                  {/* Symbol Performance */}
                  <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30 mb-4">
                    <div className="flex items-center mb-3">
                      <PieChart className="w-4 h-4 text-forex-primary mr-2" />
                      <h4 className="font-medium text-white">Symbol Performance</h4>
                    </div>
                    <div className="grid grid-cols-4 gap-2">
                      {sampleMetrics.symbols.map((symbol, index) => (
                        <div key={index} className="bg-[#0c1b31] rounded p-2 text-center">
                          <div className="text-sm font-medium text-white mb-1">{symbol.name}</div>
                          <div className="text-xs text-green-400">+{symbol.pnl}%</div>
                          <div className="text-xs text-gray-400">{symbol.winRate}% win</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Psychological Insights */}
                  <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30">
                    <div className="flex items-center mb-3">
                      <Brain className="w-4 h-4 text-forex-primary mr-2" />
                      <h4 className="font-medium text-white">Psychological Insights</h4>
                    </div>
                    <div className="space-y-2">
                      {sampleMetrics.psychologicalInsights.map((insight, index) => (
                        <div key={index} className="flex items-start bg-[#0c1b31] rounded p-2">
                          <div className={`min-w-[3px] self-stretch mr-2 rounded ${
                            insight.severity === 'high' ? 'bg-red-500' :
                            insight.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                          }`}></div>
                          <div>
                            <div className="text-sm font-medium text-white">{insight.type}</div>
                            <div className="text-xs text-gray-400">{insight.description}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="bg-[#0a1f3d] px-5 py-3 flex justify-between items-center">
                  <div className="text-xs text-gray-400">
                    Personalized insights based on your trading patterns
                  </div>
                  <Button variant="ghost" size="sm" className="text-forex-primary hover:text-white hover:bg-forex-primary/20 group" disabled>
                    Coming Soon
                    <ChevronRight className="ml-1.5 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Journal Tab Content */}
            <TabsContent value="journal" className="mt-0">
              <div className="bg-[#0c1b31]/80 backdrop-blur-md rounded-xl border border-[#2a4d7d]/30 overflow-hidden shadow-xl transform transition-all hover:shadow-glow-primary">
                {/* Header */}
                <div className="flex items-center justify-between bg-gradient-to-r from-[#0c2d5a] to-[#164279] px-6 py-4">
                  <div className="flex items-center">
                    <BookOpen className="w-5 h-5 text-forex-primary mr-2.5" />
                    <h3 className="font-bold text-white text-lg">Trading Journal</h3>
                  </div>
                  <Badge className="bg-forex-primary/20 text-forex-primary border border-forex-primary/30">
                    Notion-Style Editor
                  </Badge>
                </div>

                {/* Journal Preview */}
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {/* New Entry Card */}
                    <div
                      className="bg-[#0a1f3d] rounded-lg border border-[#2a4d7d]/30 border-dashed p-4 flex flex-col items-center justify-center h-[220px] hover:border-forex-primary/50 transition-all duration-300 cursor-pointer"
                      onMouseEnter={() => setHoveredCard('new')}
                      onMouseLeave={() => setHoveredCard(null)}
                    >
                      <div className={`w-16 h-16 rounded-full bg-[#0c1b31] flex items-center justify-center mb-4 transition-all duration-300 ${hoveredCard === 'new' ? 'bg-forex-primary/20' : ''}`}>
                        <FileText className={`w-8 h-8 transition-all duration-300 ${hoveredCard === 'new' ? 'text-forex-primary' : 'text-gray-400'}`} />
                      </div>
                      <h4 className="text-white font-medium mb-2">Create New Entry</h4>
                      <p className="text-gray-400 text-sm text-center">Document your trades, thoughts, and strategies</p>
                    </div>

                    {/* Sample Entry Card */}
                    {sampleJournalEntries.slice(0, 1).map((entry, index) => (
                      <div
                        key={index}
                        className="bg-[#0a1f3d] rounded-lg border border-[#2a4d7d]/30 p-4 h-[220px] hover:border-forex-primary/50 transition-all duration-300 cursor-pointer relative overflow-hidden"
                        onMouseEnter={() => setHoveredCard(entry.id)}
                        onMouseLeave={() => setHoveredCard(null)}
                      >
                        {/* Profitability indicator */}
                        <div className={`absolute top-0 left-0 w-full h-1 ${
                          entry.dayProfitability === 'profitable' ? 'bg-green-500' :
                          entry.dayProfitability === 'unprofitable' ? 'bg-red-500' :
                          'bg-blue-500'
                        }`}></div>

                        <div className="flex justify-between items-start mb-3">
                          <h4 className="text-white font-medium">{entry.title}</h4>
                          <div className="flex items-center">
                            {entry.mood === 'positive' && <Smile className="w-4 h-4 text-green-400" />}
                            {entry.mood === 'negative' && <Frown className="w-4 h-4 text-red-400" />}
                            {entry.mood === 'neutral' && <Meh className="w-4 h-4 text-blue-400" />}
                          </div>
                        </div>

                        <div className="text-gray-300 text-sm mb-3 line-clamp-2">{entry.content}</div>

                        <div className="flex flex-wrap gap-1 mb-3">
                          {entry.tags.map((tag, tagIndex) => (
                            <Badge key={tagIndex} className="bg-[#0c1b31] text-forex-primary border border-forex-primary/30 text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        {entry.hasImages && (
                          <div className="flex items-center text-xs text-gray-400 mb-3">
                            <ImageIcon className="w-3 h-3 mr-1" />
                            <span>Contains images</span>
                          </div>
                        )}

                        <div className="absolute bottom-4 left-4 right-4 flex justify-between items-center">
                          <div className="flex items-center text-xs text-gray-400">
                            <Calendar className="w-3 h-3 mr-1" />
                            <span>{entry.date}</span>
                          </div>

                          <div className={`text-xs font-medium ${
                            entry.pnlAmount > 0 ? 'text-green-400' :
                            entry.pnlAmount < 0 ? 'text-red-400' :
                            'text-blue-400'
                          }`}>
                            {entry.pnlAmount > 0 ? '+' : ''}{entry.pnlAmount}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Journal Features */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30">
                      <div className="w-10 h-10 rounded-full bg-[#0c1b31] flex items-center justify-center mb-3">
                        <ImageIcon className="w-5 h-5 text-forex-primary" />
                      </div>
                      <h4 className="text-white font-medium mb-1">Image Attachments</h4>
                      <p className="text-gray-400 text-xs">Attach screenshots of your charts and setups</p>
                    </div>

                    <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30">
                      <div className="w-10 h-10 rounded-full bg-[#0c1b31] flex items-center justify-center mb-3">
                        <Tag className="w-5 h-5 text-forex-primary" />
                      </div>
                      <h4 className="text-white font-medium mb-1">Custom Tags</h4>
                      <p className="text-gray-400 text-xs">Organize entries with custom tags for easy filtering</p>
                    </div>

                    <div className="bg-[#0a1f3d] rounded-lg p-4 border border-[#2a4d7d]/30">
                      <div className="w-10 h-10 rounded-full bg-[#0c1b31] flex items-center justify-center mb-3">
                        <TrendingUp className="w-5 h-5 text-forex-primary" />
                      </div>
                      <h4 className="text-white font-medium mb-1">Performance Tracking</h4>
                      <p className="text-gray-400 text-xs">Track your daily P&L and emotional state</p>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="bg-[#0a1f3d] px-5 py-3 flex justify-between items-center">
                  <div className="text-xs text-gray-400">
                    Document your trading journey with our Notion-style journal
                  </div>
                  <Link to="/journal">
                    <Button variant="ghost" size="sm" className="text-forex-primary hover:text-white hover:bg-forex-primary/20 group">
                      Open Journal
                      <ChevronRight className="ml-1.5 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </section>
  );
};

export default FeatureShowcase;
