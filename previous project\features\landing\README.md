# Landing Feature

This directory contains components for the landing page of TradeChampionX. The landing page is designed to introduce users to the platform, explain how it works, and encourage sign-ups.

## Components

### Hero Section

- **Hero**: The main hero section of the landing page with a call-to-action

### Informational Sections

- **HowItWorks**: Explains the process of participating in trading challenges
- **WhyChooseUs**: Highlights the advantages of TradeChampionX over traditional prop firms
- **RulesSection**: Explains the rules of the trading challenges
- **PrizeDistributionExplainer**: Explains how prizes are distributed
- **PrizePoolVisual**: Visual representation of the prize pool
- **WalletCreditsVisual**: Visual representation of wallet credits
- **GamificationSection**: Explains the gamification elements of the platform

### Social Proof

- **TestimonialsSection**: Displays testimonials from users
- **TrustPillars**: Displays trust indicators
- **TrustSignals**: Displays trust signals like security measures and partnerships
- **DiscordSection**: Promotes the Discord community

### FAQ Sections

- **FAQSection**: Standard FAQ section with accordion
- **FAQSectionDark**: Dark-themed FAQ section
- **FAQSectionSidebar**: FAQ section with sidebar navigation
- **FAQSection.new**: New version of the FAQ section (in development)

### Pricing

- **PricingTable**: Displays pricing information for different challenge tiers
- **LeaderboardTeaser**: Teaser for the leaderboard feature

## Usage

Components from this feature can be imported directly from the feature's index file:

```tsx
import { Hero, HowItWorks, FAQSection } from '@/features/landing';
```

## Design Guidelines

- Use consistent color schemes and typography as defined in the design system
- Ensure all sections are responsive and look good on all device sizes
- Use animations sparingly to highlight important information
- Ensure all text is readable with good contrast

## Content Guidelines

- Keep text concise and focused on benefits
- Use clear calls-to-action
- Highlight what makes TradeChampionX different from competitors
- Address common questions and concerns in the FAQ sections
