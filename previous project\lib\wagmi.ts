/**
 * Wagmi Configuration
 * @description Configuration for wallet connection with Base L2 support
 * @version 1.0.0
 * @status stable
 */

import { getDefaultConfig } from '@rainbow-me/rainbowkit';
import { base, baseSepolia, mainnet, sepolia } from 'wagmi/chains';

// Get environment variables
const projectId = import.meta.env.VITE_WALLETCONNECT_PROJECT_ID || 'your-project-id';
const appName = 'TradeChampionX';
const appDescription = 'Crypto-native trading challenges platform';
const appUrl = 'https://tradechampionx.com';
const appIcon = 'https://tradechampionx.com/logo.png';

// Configure chains - Base L2 for production, Base Sepolia for development
// Also include Ethereum mainnet and sepolia for broader wallet compatibility
const chains = [
  base, // Base mainnet
  baseSepolia, // Base testnet for development
  mainnet, // Ethereum mainnet for broader compatibility
  sepolia, // Ethereum testnet for development
] as const;

// Create wagmi config with RainbowKit
export const config = getDefaultConfig({
  appName,
  projectId,
  chains,
  ssr: false, // We're using Vite, not Next.js
});

// Export chains for use in other parts of the app
export { chains };

// Export chain IDs for easy reference
export const CHAIN_IDS = {
  BASE: base.id,
  BASE_SEPOLIA: baseSepolia.id,
  MAINNET: mainnet.id,
  SEPOLIA: sepolia.id,
} as const;

// USDC contract addresses on different chains
export const USDC_ADDRESSES = {
  [base.id]: '******************************************', // USDC on Base
  [baseSepolia.id]: '******************************************', // USDC on Base Sepolia
} as const;

// Get USDC address for current chain
export const getUSDCAddress = (chainId: number): string => {
  return USDC_ADDRESSES[chainId as keyof typeof USDC_ADDRESSES] || USDC_ADDRESSES[base.id];
};
