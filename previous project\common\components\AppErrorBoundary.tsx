/**
 * App Error Boundary Component
 * @description A top-level error boundary for the entire application
 * @version 1.0.0
 * @status stable
 */

import React, { ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';
import { AlertTriangle, Home, RefreshCw } from 'lucide-react';
import { Button } from './ui/button';

interface AppErrorBoundaryProps {
  children: ReactNode;
}

/**
 * Custom fallback UI for critical application errors
 */
const CriticalErrorFallback: React.FC<{ onReset: () => void }> = ({ onReset }) => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-forex-dark p-4">
      <div className="max-w-md w-full bg-forex-darker border border-red-500/30 rounded-lg shadow-lg p-6 text-center">
        <div className="flex justify-center mb-4">
          <div className="bg-red-500/10 p-3 rounded-full">
            <AlertTriangle className="h-12 w-12 text-red-500" />
          </div>
        </div>

        <h1 className="text-2xl font-bold text-white mb-2">Application Error</h1>

        <p className="text-forex-muted mb-6">
          We've encountered a critical error. Please try refreshing the page or return to the home page.
        </p>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            variant="outline"
            className="border-red-500/30 hover:bg-red-500/10"
            onClick={onReset}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Application
          </Button>

          <Button
            onClick={() => window.location.href = '/'}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Home className="mr-2 h-4 w-4" />
            Return to Home
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * App-level error boundary component
 * Catches errors that occur anywhere in the application
 */
const AppErrorBoundary: React.FC<AppErrorBoundaryProps> = ({ children }) => {
  const handleReset = () => {
    // Force a full page reload to reset the application state
    window.location.reload();
  };

  return (
    <ErrorBoundary
      componentName="Application"
      onReset={handleReset}
      fallback={<CriticalErrorFallback onReset={handleReset} />}
    >
      {children}
    </ErrorBoundary>
  );
};

export default AppErrorBoundary;
