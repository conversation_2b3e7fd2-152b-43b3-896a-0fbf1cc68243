/**
 * Challenges Page
 * @description Page for browsing and joining trading challenges
 * @version 1.0.0
 * @status stable
 */

import React, { useState } from 'react';
import { ChallengeBrowser, ChallengeManagementHub } from '@/features/challenges';
import { Toaster } from '@/common/components/ui/sonner';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/common/components/ui';
import { Trophy, List } from 'lucide-react';

/**
 * ChallengesPage component
 * @returns ChallengesPage component
 */
const ChallengesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('browser');

  return (
    <>
      <div className="min-h-screen bg-forex-dark text-forex-light">
        <div className="container mx-auto px-4 py-8">
          <Tabs 
            defaultValue="browser" 
            value={activeTab} 
            onValueChange={setActiveTab}
            className="w-full"
          >
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-bold tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-primary">
                  Challenges
                </span>
              </h1>
              <TabsList className="grid grid-cols-2 w-[400px]">
                <TabsTrigger value="browser" className="flex items-center gap-2">
                  <List className="h-4 w-4" />
                  Browse Challenges
                </TabsTrigger>
                <TabsTrigger value="management" className="flex items-center gap-2">
                  <Trophy className="h-4 w-4" />
                  Your Challenges
                </TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="browser" className="mt-0">
              <ChallengeBrowser />
            </TabsContent>
            
            <TabsContent value="management" className="mt-0">
              <ChallengeManagementHub />
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <Toaster position="top-right" />
    </>
  );
};

export default ChallengesPage;
