# Authentication System

This directory contains components and utilities related to user authentication in TradeChampionX.

## Components

### ProtectedRoute

The `ProtectedRoute` component is used to protect routes that require authentication. It redirects unauthenticated users to the login page.

**Usage:**
```tsx
<ProtectedRoute>
  <DashboardPage />
</ProtectedRoute>
```

### VerificationLoading

The `VerificationLoading` component displays a loading indicator while verifying user authentication status. It provides visual feedback during the authentication process.

**Usage:**
```tsx
<VerificationLoading message="Verifying your account..." />
```

### VerificationNotFound

The `VerificationNotFound` component displays an error message when user verification fails. It provides a clear message and options for the user to resolve the issue.

**Usage:**
```tsx
<VerificationNotFound message="We couldn't verify your account." />
```

### ActiveSessionHandler

The `ActiveSessionHandler` component provides a simple link to clear active sessions when a user encounters the "Session already active" error during sign-in or sign-up.

**Usage:**
```tsx
<ActiveSessionHandler />
```

## Services

### userService

The `userService` provides hooks for managing user data and synchronization with the backend.

**Usage:**
```tsx
const { mutate: syncUser } = useSyncUser();
const { data: currentUser } = useCurrentUser();
```

## Integration with Clerk

TradeChampionX uses Clerk for authentication. The components in this directory are designed to work with Clerk's authentication flow.

## Authentication Flow

1. User navigates to login/signup page
2. User authenticates with Clerk
3. On successful authentication, user is redirected to the protected route
4. Protected routes check authentication status using the `ProtectedRoute` component
5. If authentication is being verified, the `VerificationLoading` component is displayed
6. If authentication fails, the `VerificationNotFound` component is displayed
7. The JWT token is sent with API requests to authenticate the user
8. The backend validates the JWT token using the Clerk JWT verification key

## Backend Integration

The backend includes a webhook handler that receives events from Clerk when users sign up, update their profiles, or perform other actions. This keeps the database in sync with Clerk's user data.

## Protected Routes

Routes that require authentication are protected using the `validateJWT` middleware. This middleware verifies the JWT token and extracts user information.

## Admin Routes

Routes that require admin access are protected using the `requireAdmin` middleware. This middleware checks if the user has the admin role.

## Best Practices

- Always wrap protected content with the `ProtectedRoute` component
- Use the `VerificationLoading` component to provide feedback during authentication checks
- Use the `VerificationNotFound` component to handle authentication failures gracefully

