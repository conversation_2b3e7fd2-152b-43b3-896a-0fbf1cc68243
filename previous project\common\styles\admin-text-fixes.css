/* Admin Panel Text Fixes
 * This file contains CSS rules to ensure all text in the admin panel is white or light-colored
 * for better visibility on dark backgrounds.
 */

/* Make all text in admin panel white by default */
[class*="admin-"] *,
.admin-panel *,
[class*="bg-forex-dark"] *,
[class*="bg-forex-darker"] * {
  color: #ffffff !important;
}

/* Form inputs and selects should have white text */
.admin-panel input,
.admin-panel select,
.admin-panel textarea,
[class*="bg-forex-dark"] input,
[class*="bg-forex-dark"] select,
[class*="bg-forex-dark"] textarea,
[class*="bg-forex-darker"] input,
[class*="bg-forex-darker"] select,
[class*="bg-forex-darker"] textarea {
  color: #ffffff !important;
}

/* Dropdown menus and their items */
.admin-panel [role="menu"],
.admin-panel [role="menuitem"],
[class*="bg-forex-dark"] [role="menu"],
[class*="bg-forex-dark"] [role="menuitem"],
[class*="bg-forex-darker"] [role="menu"],
[class*="bg-forex-darker"] [role="menuitem"] {
  color: #ffffff !important;
}

/* Dialog content */
.admin-panel [role="dialog"] *,
[class*="bg-forex-dark"] [role="dialog"] *,
[class*="bg-forex-darker"] [role="dialog"] * {
  color: #ffffff !important;
}

/* Table cells */
.admin-panel td,
.admin-panel th,
[class*="bg-forex-dark"] td,
[class*="bg-forex-dark"] th,
[class*="bg-forex-darker"] td,
[class*="bg-forex-darker"] th {
  color: #ffffff !important;
}

/* Card content */
.admin-panel .card *,
.admin-panel [class*="card-"] *,
[class*="bg-forex-dark"] .card *,
[class*="bg-forex-dark"] [class*="card-"] *,
[class*="bg-forex-darker"] .card *,
[class*="bg-forex-darker"] [class*="card-"] * {
  color: #ffffff !important;
}

/* Specific components */
.admin-panel .dropdown-menu-content *,
.admin-panel .select-content *,
.admin-panel .dialog-content *,
[class*="bg-forex-dark"] .dropdown-menu-content *,
[class*="bg-forex-dark"] .select-content *,
[class*="bg-forex-dark"] .dialog-content *,
[class*="bg-forex-darker"] .dropdown-menu-content *,
[class*="bg-forex-darker"] .select-content *,
[class*="bg-forex-darker"] .dialog-content * {
  color: #ffffff !important;
}

/* Target specific UI components */
.dropdown-menu-content,
.select-content,
.dialog-content,
.popover-content,
.tooltip-content {
  color: #ffffff !important;
  background-color: var(--forex-darker, #0f172a) !important;
}

/* Target all shadcn UI components in admin context */
[class*="bg-forex-dark"] [class*="ui-"],
[class*="bg-forex-darker"] [class*="ui-"] {
  color: #ffffff !important;
}

/* Force white text for all admin components */
[id*="admin"] *,
[class*="admin"] *,
[href*="admin"] * {
  color: #ffffff !important;
}

/* Target specific components by their class names */
.bg-forex-darker *,
.bg-forex-dark * {
  color: #ffffff !important;
}

/* Target all text in the admin layout */
#admin-layout *,
.admin-layout * {
  color: #ffffff !important;
}

/* Ensure placeholder text is visible */
[class*="bg-forex-dark"] input::placeholder,
[class*="bg-forex-darker"] input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* Ensure text in form controls is white */
[class*="bg-forex-dark"] .form-control,
[class*="bg-forex-darker"] .form-control,
[class*="bg-forex-dark"] .input,
[class*="bg-forex-darker"] .input,
[class*="bg-forex-dark"] .select,
[class*="bg-forex-darker"] .select,
[class*="bg-forex-dark"] .textarea,
[class*="bg-forex-darker"] .textarea {
  color: #ffffff !important;
}

/* Ensure text in buttons is white */
[class*="bg-forex-dark"] .button,
[class*="bg-forex-darker"] .button,
[class*="bg-forex-dark"] button,
[class*="bg-forex-darker"] button {
  color: #ffffff !important;
}

/* Ensure text in badges is visible */
[class*="bg-forex-dark"] .badge,
[class*="bg-forex-darker"] .badge {
  color: #ffffff !important;
}

/* Ensure text in dropdowns is white */
.dropdown-menu-content *,
.select-content * {
  color: #ffffff !important;
}

/* Ensure text in dialogs is white */
.dialog-content *,
.dialog-header *,
.dialog-footer *,
.dialog-title *,
.dialog-description * {
  color: #ffffff !important;
}

/* Ensure text in tabs is white */
.tabs-list *,
.tabs-trigger *,
.tabs-content * {
  color: #ffffff !important;
}

/* Ensure text in tables is white */
.table *,
.table-header *,
.table-row *,
.table-cell *,
.table-head *,
.table-body * {
  color: #ffffff !important;
}

/* Ensure text in cards is white */
.card *,
.card-header *,
.card-title *,
.card-description *,
.card-content *,
.card-footer * {
  color: #ffffff !important;
}

/* Ensure text in forms is white */
.form *,
.form-item *,
.form-label *,
.form-control *,
.form-message *,
.form-description * {
  color: #ffffff !important;
}

/* Ensure text in alerts is white */
.alert *,
.alert-title *,
.alert-description * {
  color: #ffffff !important;
}

/* Ensure text in toasts is white */
.toast *,
.toast-title *,
.toast-description * {
  color: #ffffff !important;
}

/* Ensure text in tooltips is white */
.tooltip *,
.tooltip-content * {
  color: #ffffff !important;
}

/* Ensure text in popovers is white */
.popover *,
.popover-content * {
  color: #ffffff !important;
}

/* Ensure text in sheets is white */
.sheet *,
.sheet-content *,
.sheet-header *,
.sheet-footer * {
  color: #ffffff !important;
}

/* Ensure text in command is white */
.command *,
.command-input *,
.command-list *,
.command-item * {
  color: #ffffff !important;
}

/* Ensure text in navigation menu is white */
.navigation-menu *,
.navigation-menu-list *,
.navigation-menu-item * {
  color: #ffffff !important;
}

/* Ensure text in context menu is white */
.context-menu *,
.context-menu-content *,
.context-menu-item * {
  color: #ffffff !important;
}

/* Ensure text in hover card is white */
.hover-card *,
.hover-card-content * {
  color: #ffffff !important;
}

/* Ensure text in menubar is white */
.menubar *,
.menubar-menu *,
.menubar-item * {
  color: #ffffff !important;
}

/* Ensure text in radio group is white */
.radio-group *,
.radio-group-item * {
  color: #ffffff !important;
}

/* Ensure text in scroll area is white */
.scroll-area *,
.scroll-area-viewport * {
  color: #ffffff !important;
}

/* Ensure text in select is white */
.select *,
.select-trigger *,
.select-value *,
.select-icon * {
  color: #ffffff !important;
}

/* Ensure text in separator is white */
.separator * {
  color: #ffffff !important;
}

/* Ensure text in slider is white */
.slider *,
.slider-track *,
.slider-range *,
.slider-thumb * {
  color: #ffffff !important;
}

/* Ensure text in switch is white */
.switch *,
.switch-thumb * {
  color: #ffffff !important;
}

/* Ensure text in textarea is white */
.textarea * {
  color: #ffffff !important;
}

/* Ensure text in toggle is white */
.toggle * {
  color: #ffffff !important;
}

/* Ensure text in toggle group is white */
.toggle-group *,
.toggle-group-item * {
  color: #ffffff !important;
}

/* Target shadcn UI components by their class names */
[class*="bg-forex-dark"] [class*="Dialog"],
[class*="bg-forex-dark"] [class*="Dropdown"],
[class*="bg-forex-dark"] [class*="Select"],
[class*="bg-forex-dark"] [class*="Tabs"],
[class*="bg-forex-dark"] [class*="Card"],
[class*="bg-forex-dark"] [class*="Table"],
[class*="bg-forex-dark"] [class*="Form"],
[class*="bg-forex-dark"] [class*="Input"],
[class*="bg-forex-dark"] [class*="Button"],
[class*="bg-forex-dark"] [class*="Badge"],
[class*="bg-forex-darker"] [class*="Dialog"],
[class*="bg-forex-darker"] [class*="Dropdown"],
[class*="bg-forex-darker"] [class*="Select"],
[class*="bg-forex-darker"] [class*="Tabs"],
[class*="bg-forex-darker"] [class*="Card"],
[class*="bg-forex-darker"] [class*="Table"],
[class*="bg-forex-darker"] [class*="Form"],
[class*="bg-forex-darker"] [class*="Input"],
[class*="bg-forex-darker"] [class*="Button"],
[class*="bg-forex-darker"] [class*="Badge"] {
  color: #ffffff !important;
}

/* Target specific admin components */
.ChallengeCreationDialog *,
.ChallengeManager *,
.AdminDashboard *,
.UserManager *,
.TransactionManager *,
.SettingsManager *,
.MetricsDashboard *,
.AdminNotifications *,
.PrizeDistributionManager *,
.AuditLogViewer * {
  color: #ffffff !important;
}

/* Target all elements in admin routes */
[class*="admin-route"] *,
[id*="admin-route"] * {
  color: #ffffff !important;
}

/* Fix for Clerk components in admin panel */
.admin-panel .cl-component *,
.admin-panel .cl-rootBox *,
.admin-panel .cl-card *,
.admin-panel .cl-form *,
.admin-panel .cl-socialButtonsBlockButton *,
.admin-panel .cl-footerAction *,
.admin-panel .cl-footerActionText *,
.admin-panel .cl-footerActionLink *,
.admin-panel .cl-identityPreview *,
.admin-panel .cl-headerTitle *,
.admin-panel .cl-headerSubtitle *,
.admin-panel .cl-formButtonPrimary *,
.admin-panel .cl-formFieldLabel *,
.admin-panel .cl-formFieldInput * {
  color: #ffffff !important;
}

/* Fix for shadcn UI components in admin panel */
.admin-panel [class*="dialog"] *,
.admin-panel [class*="dropdown"] *,
.admin-panel [class*="select"] *,
.admin-panel [class*="tabs"] *,
.admin-panel [class*="card"] *,
.admin-panel [class*="table"] *,
.admin-panel [class*="form"] *,
.admin-panel [class*="input"] *,
.admin-panel [class*="button"] *,
.admin-panel [class*="badge"] * {
  color: #ffffff !important;
}

/* Force all text in admin panel to be white */
#root [href*="/admin"] *,
#root [href*="/admin/"] *,
#root [class*="admin"] *,
#root [id*="admin"] *,
#root [class*="bg-forex-dark"] *,
#root [class*="bg-forex-darker"] * {
  color: #ffffff !important;
}

/* Highest specificity override for any remaining dark text */
html body #root .admin-panel *,
html body #root #admin-layout *,
html body #root [class*="bg-forex-dark"] *,
html body #root [class*="bg-forex-darker"] * {
  color: #ffffff !important;
}
