/**
 * withErrorBoundary HOC
 * @description Higher-order component that wraps a component with an error boundary
 * @version 1.0.0
 * @status stable
 */

import React, { ComponentType, ReactNode } from 'react';
import ErrorBoundary from '@/common/components/ErrorBoundary';

interface WithErrorBoundaryOptions {
  fallback?: ReactNode;
  componentName?: string;
  onReset?: () => void;
  containError?: boolean;
}

/**
 * Higher-order component that wraps a component with an error boundary
 * @param Component - The component to wrap
 * @param options - Options for the error boundary
 * @returns The wrapped component
 */
function withErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  options: WithErrorBoundaryOptions = {}
): React.FC<P> {
  const { fallback, componentName, onReset, containError } = options;
  
  // Use the component's display name or function name for better error messages
  const displayName = componentName || Component.displayName || Component.name || 'Component';
  
  // Create the wrapped component
  const WrappedComponent: React.FC<P> = (props: P) => {
    return (
      <ErrorBoundary
        fallback={fallback}
        componentName={displayName}
        onReset={onReset}
        containError={containError}
      >
        <Component {...props} />
      </ErrorBoundary>
    );
  };
  
  // Set the display name for better debugging
  WrappedComponent.displayName = `withErrorBoundary(${displayName})`;
  
  return WrappedComponent;
}

export default withErrorBoundary;
