import React, { createContext, useContext, ReactNode } from 'react';
import { useIsMobile, useScreenSize, useBreakpoint, ScreenSize } from '@/common/hooks';

/**
 * Interface for the responsive context
 */
interface ResponsiveContextType {
  /** Whether the screen is mobile (< 768px) */
  isMobile: boolean;
  /** Current screen size based on Tailwind breakpoints */
  screenSize: ScreenSize;
  /** Function to check if the screen matches a specific breakpoint or range */
  isBreakpoint: (min: ScreenSize, max?: ScreenSize) => boolean;
  /** Whether the screen is portrait orientation */
  isPortrait: boolean;
  /** Whether the screen supports touch */
  isTouch: boolean;
}

/**
 * Create the responsive context
 */
const ResponsiveContext = createContext<ResponsiveContextType | undefined>(undefined);

/**
 * Props for the ResponsiveProvider component
 */
interface ResponsiveProviderProps {
  /** Child components */
  children: ReactNode;
}

/**
 * Provider component for responsive context
 * 
 * Provides responsive information to all child components
 * 
 * @component
 * @example
 * <ResponsiveProvider>
 *   <App />
 * </ResponsiveProvider>
 */
export const ResponsiveProvider: React.FC<ResponsiveProviderProps> = ({ children }) => {
  const isMobile = useIsMobile();
  const screenSize = useScreenSize();
  
  // Check if device is in portrait orientation
  const [isPortrait, setIsPortrait] = React.useState(
    typeof window !== 'undefined' ? window.matchMedia('(orientation: portrait)').matches : false
  );
  
  // Check if device supports touch
  const [isTouch, setIsTouch] = React.useState(
    typeof window !== 'undefined' ? 'ontouchstart' in window : false
  );
  
  // Function to check if the screen matches a specific breakpoint or range
  const isBreakpoint = (min: ScreenSize, max?: ScreenSize) => {
    return useBreakpoint(min, max);
  };
  
  // Update orientation on change
  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia('(orientation: portrait)');
    
    const handleOrientationChange = (e: MediaQueryListEvent) => {
      setIsPortrait(e.matches);
    };
    
    // Modern browsers
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleOrientationChange);
      return () => mediaQuery.removeEventListener('change', handleOrientationChange);
    }
    
    // Legacy browsers
    mediaQuery.addListener(handleOrientationChange);
    return () => mediaQuery.removeListener(handleOrientationChange);
  }, []);
  
  const value = {
    isMobile,
    screenSize,
    isBreakpoint,
    isPortrait,
    isTouch,
  };
  
  return (
    <ResponsiveContext.Provider value={value}>
      {children}
    </ResponsiveContext.Provider>
  );
};

/**
 * Hook to use the responsive context
 * @returns {ResponsiveContextType} Responsive context
 * @throws {Error} If used outside of a ResponsiveProvider
 */
export const useResponsive = (): ResponsiveContextType => {
  const context = useContext(ResponsiveContext);
  
  if (context === undefined) {
    throw new Error('useResponsive must be used within a ResponsiveProvider');
  }
  
  return context;
};
