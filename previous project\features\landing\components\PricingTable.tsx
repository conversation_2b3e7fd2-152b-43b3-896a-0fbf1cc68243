import { <PERSON><PERSON> } from "@/common/components/ui/button";
import {
  ArrowRight,
  Check,
  Calendar,
  Clock,
  TrendingDown,
  BarChart3,
  Timer,
  Award,
  ShieldAlert,
  Percent,
  Zap,
  AlertTriangle,
  Ban,
  Clock3,
  Hourglass,
  Wallet,
  Trophy,
  ChevronRight
} from "lucide-react";
import { cn } from "@/common/utils";
import { Link } from "react-router-dom";
import React, { useState, useEffect } from "react";

import { Badge } from "@/common/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/common/components/ui/tooltip";

const PricingTable = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [activeTab, setActiveTab] = useState<string>("overview");

  useEffect(() => {
    // Update current date every minute
    const timer = setInterval(() => {
      setCurrentDate(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Function to check if a challenge is enrollable
  const getChallengeStatus = (type: string) => {
    const estOffset = -4; // EST timezone offset
    const estDate = new Date(currentDate.getTime() + (currentDate.getTimezoneOffset() + estOffset * 60) * 60000);

    const currentDay = estDate.getDay(); // 0 is Sunday, 1 is Monday, etc.
    const currentHour = estDate.getHours();
    const currentDateNum = estDate.getDate();
    const currentMonth = estDate.getMonth();

    if (type === "daily") {
      // Daily challenges start at midnight EST
      // Allow enrollment for next day's challenge
      return {
        enrollable: true,
        message: "Start Trading",
        nextStart: new Date(estDate.setHours(24, 0, 0, 0))
      };
    } else if (type === "weekly") {
      // Weekly challenges start on Monday at midnight EST
      const daysUntilMonday = (currentDay === 0) ? 1 : ((7 - currentDay) + 1);
      const nextMonday = new Date(estDate);
      nextMonday.setDate(estDate.getDate() + daysUntilMonday);
      nextMonday.setHours(0, 0, 0, 0);

      return {
        enrollable: true,
        message: "Start Trading",
        nextStart: nextMonday
      };
    } else if (type === "monthly") {
      // Monthly challenges start on the 1st of each month
      const nextMonth = new Date(estDate);
      nextMonth.setMonth(estDate.getMonth() + 1);
      nextMonth.setDate(1);
      nextMonth.setHours(0, 0, 0, 0);

      return {
        enrollable: true,
        message: "Start Trading",
        nextStart: nextMonth
      };
    }

    return {
      enrollable: false,
      message: "Enrollment closed",
      nextStart: null
    };
  };

  const formatNextDate = (date: Date | null) => {
    if (!date) return "";

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const plans = [
    {
      name: "Daily Challenge",
      price: "$20",
      initialBalance: "$10,000",
      duration: "24 hours",
      type: "daily",
      maxDrawdown: "4%",
      maxDailyDrawdown: "N/A",
      maxRiskPerTrade: "2%",
      minTradeDuration: "2 min",
      minTrades: "1 trade minimum",
      maxDailyLoss: "N/A",
      rules: [
        {
          name: "No Hedging",
          description: "No buy/sell same symbol at same time",
          icon: <Ban className="w-4 h-4" />
        },
        {
          name: "No Martingale",
          description: "No high risk averaging strategies",
          icon: <AlertTriangle className="w-4 h-4" />
        },
        {
          name: "Min Trade Duration",
          description: "2 min minimum hold time (anti-scalping)",
          icon: <Clock3 className="w-4 h-4" />
        },
        {
          name: "Min Number of Trades",
          description: "1 trade minimum",
          icon: <BarChart3 className="w-4 h-4" />
        }
      ],
      benefits: [
        "Next-day payouts",
        "Perfect for testing strategies",
        "Low entry barrier",
        "Quick feedback on performance"
      ],
      popular: false,
      ctaText: "Join Daily Challenge",
      ctaLink: "/dashboard?type=daily",
      color: "purple-500",
      gradient: "from-purple-500/20 to-purple-500/5"
    },
    {
      name: "Weekly Challenge",
      price: "$50",
      initialBalance: "$50,000",
      duration: "7 days",
      type: "weekly",
      maxDrawdown: "8%",
      maxDailyDrawdown: "4%",
      maxRiskPerTrade: "2%",
      minTradeDuration: "2 min",
      minTrades: "3 trades minimum (or 1 Swing Trade held 2 days)",
      maxDailyLoss: "4%",
      rules: [
        {
          name: "No Hedging",
          description: "No buy/sell same symbol at same time",
          icon: <Ban className="w-4 h-4" />
        },
        {
          name: "No Martingale",
          description: "No high risk averaging strategies",
          icon: <AlertTriangle className="w-4 h-4" />
        },
        {
          name: "Min Trade Duration",
          description: "2 min minimum hold time (anti-scalping)",
          icon: <Clock3 className="w-4 h-4" />
        },
        {
          name: "Min Number of Trades",
          description: "3 trades minimum (or 1 Swing Trade held 2 days)",
          icon: <BarChart3 className="w-4 h-4" />
        }
      ],
      benefits: [
        "Weekly performance reports",
        "Balanced risk/reward ratio",
        "Swing trading friendly",
        "Higher potential rewards"
      ],
      popular: true,
      ctaText: "Join Weekly Challenge",
      ctaLink: "/dashboard?type=weekly",
      color: "forex-accent",
      gradient: "from-forex-accent/20 to-forex-accent/5"
    },
    {
      name: "Monthly Championship",
      price: "$99",
      initialBalance: "$100,000",
      duration: "30 days",
      type: "monthly",
      maxDrawdown: "10%",
      maxDailyDrawdown: "4%",
      maxRiskPerTrade: "2%",
      minTradeDuration: "2 min",
      minTrades: "6 different trading days OR 3 Swing Trades held for 2 days each",
      maxDailyLoss: "4%",
      rules: [
        {
          name: "No Hedging",
          description: "No buy/sell same symbol at same time",
          icon: <Ban className="w-4 h-4" />
        },
        {
          name: "No Martingale",
          description: "No high risk averaging strategies",
          icon: <AlertTriangle className="w-4 h-4" />
        },
        {
          name: "Min Trade Duration",
          description: "2 min minimum hold time (anti-scalping)",
          icon: <Clock3 className="w-4 h-4" />
        },
        {
          name: "Min Number of Trades",
          description: "6 different trading days OR 3 Swing Trades held for 2 days each",
          icon: <BarChart3 className="w-4 h-4" />
        }
      ],
      benefits: [
        "Largest prize pool",
        "Weekly performance reports",
        "Most flexible trading conditions",
        "Highest potential rewards"
      ],
      popular: false,
      ctaText: "Join Monthly Challenge",
      ctaLink: "/dashboard?type=monthly",
      color: "forex-secondary",
      gradient: "from-forex-secondary/20 to-forex-secondary/5"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-forex-dark to-[#0a1a2f] relative overflow-hidden" id="pricing">
      {/* Background pattern and decorative elements - consistent with other sections */}
      <div className="absolute inset-0 bg-[url('/bg-pattern.svg')] opacity-10"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-forex-primary/5 to-forex-accent/5"></div>
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-forex-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-forex-accent/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <span className="inline-block px-4 py-1.5 bg-forex-primary/20 text-forex-primary font-medium rounded-full mb-4 shadow-sm backdrop-blur-sm border border-forex-primary/40">
            Choose Your Challenge
          </span>
          <h2 className="text-3xl md:text-4xl font-bold heading-primary mb-4">
            Select Your Trading Challenge
          </h2>
          <p className="text-lg paragraph-bright">
            Pick the challenge that matches your trading style, timeframe, and risk preference
          </p>
        </div>

        {/* Challenge Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {plans.map((plan, index) => {
            const status = getChallengeStatus(plan.type);
            const planColor = plan.color === 'purple-500' ? 'from-purple-500 to-indigo-500' :
                             plan.color === 'forex-accent' ? 'from-forex-accent to-forex-primary' :
                             'from-forex-secondary to-forex-accent';

            return (
              <div
                key={plan.name}
                data-aos="fade-up"
                data-aos-duration="800"
                data-aos-delay={index * 100}
                className={cn(
                  "glass-card overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 bg-forex-card/80 backdrop-blur-md border border-forex-border/20 w-full",
                  plan.popular ? `border-t-4 border-${plan.color}` : ""
                )}
              >
                {plan.popular && (
                  <div className={`bg-gradient-to-r from-${plan.color} to-${
                    plan.color === 'purple-500' ? 'indigo-500' :
                    plan.color === 'forex-accent' ? 'forex-primary' :
                    'forex-accent'
                  } text-white text-center py-2 font-medium relative`}>
                    Most Popular
                    <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent" style={{
                      borderTopColor:
                        plan.color === 'purple-500' ? '#6366f1' :
                        plan.color === 'forex-accent' ? '#0ea5e9' :
                        '#10b981'
                    }}></div>
                  </div>
                )}

                <div className="p-8">
                  {/* Header */}
                  <div className="mb-6">
                    <h3 className={`text-2xl font-bold ${plan.color === 'purple-500' ? 'text-purple-500' : `text-${plan.color}`} mb-2 relative`}>
                      {plan.name}
                      {!plan.popular && <div className={`h-1 w-12 ${plan.color === 'purple-500' ? 'bg-purple-500' : `bg-${plan.color}`} rounded-full mt-1`}></div>}
                    </h3>

                    <div className="flex items-end mb-4">
                      <span className={`text-4xl font-bold bg-gradient-to-r ${plan.color === 'purple-500' ? 'from-purple-500' : `from-${plan.color}`} to-white bg-clip-text text-transparent`}>
                        {plan.price}
                      </span>
                      <span className="text-forex-light ml-2 pb-1">
                        / {plan.duration}
                      </span>
                    </div>

                    <div className={`bg-gradient-to-r ${plan.color === 'purple-500' ? 'from-purple-500/20 to-purple-500/5' : plan.gradient} rounded-md p-4 border ${plan.color === 'purple-500' ? 'border-purple-500/30' : `border-${plan.color}/30`} shadow-md`}>
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-forex-light text-sm font-medium">Initial Balance</div>
                          <div className="text-white font-bold text-2xl">{plan.initialBalance}</div>
                        </div>
                        <div className={`w-10 h-10 rounded-full ${plan.color === 'purple-500' ? 'bg-purple-500/20' : `bg-${plan.color}/20`} flex items-center justify-center`}>
                          <Wallet className={`w-6 h-6 ${plan.color === 'purple-500' ? 'text-purple-500' : `text-${plan.color}`}`} />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tabs for different sections */}
                  <div className="mb-8 px-0">
                    <div className="flex space-x-1 mb-6">
                      <button
                        onClick={() => setActiveTab("overview")}
                        className={`flex-1 py-2 px-2.5 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-start ${
                          activeTab === "overview"
                            ? `bg-gradient-to-r from-${plan.color} to-${plan.color === 'forex-primary' ? 'forex-accent' : plan.color === 'forex-accent' ? 'forex-primary' : 'forex-accent'} text-white shadow-md`
                            : `bg-forex-dark text-forex-light/80 hover:bg-forex-dark/80 hover:text-forex-light border border-forex-border/20`
                        }`}
                      >
                        <div className={`w-4 h-4 rounded-full flex items-center justify-center mr-1 ${activeTab === "overview" ? "bg-white/20" : `bg-${plan.color}/20`}`}>
                          <BarChart3 className={`w-3 h-3 ${activeTab === "overview" ? "text-white" : `text-${plan.color}`}`} />
                        </div>
                        Overview
                      </button>
                      <button
                        onClick={() => setActiveTab("rules")}
                        className={`flex-1 py-2 px-2.5 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-start ${
                          activeTab === "rules"
                            ? `bg-gradient-to-r from-${plan.color} to-${plan.color === 'forex-primary' ? 'forex-accent' : plan.color === 'forex-accent' ? 'forex-primary' : 'forex-accent'} text-white shadow-md`
                            : `bg-forex-dark text-forex-light/80 hover:bg-forex-dark/80 hover:text-forex-light border border-forex-border/20`
                        }`}
                      >
                        <div className={`w-4 h-4 rounded-full flex items-center justify-center mr-1 ${activeTab === "rules" ? "bg-white/20" : `bg-${plan.color}/20`}`}>
                          <ShieldAlert className={`w-3 h-3 ${activeTab === "rules" ? "text-white" : `text-${plan.color}`}`} />
                        </div>
                        Rules
                      </button>
                      <button
                        onClick={() => setActiveTab("benefits")}
                        className={`flex-1 py-2 px-2.5 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-start ${
                          activeTab === "benefits"
                            ? `bg-gradient-to-r from-${plan.color} to-${plan.color === 'forex-primary' ? 'forex-accent' : plan.color === 'forex-accent' ? 'forex-primary' : 'forex-accent'} text-white shadow-md`
                            : `bg-forex-dark text-forex-light/80 hover:bg-forex-dark/80 hover:text-forex-light border border-forex-border/20`
                        }`}
                      >
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${activeTab === "benefits" ? "bg-white/20" : `bg-${plan.color}/20`}`}>
                          <Award className={`w-3.5 h-3.5 ${activeTab === "benefits" ? "text-white" : `text-${plan.color}`}`} />
                        </div>
                        Benefits
                      </button>
                    </div>

                    {/* Overview Tab */}
                    {activeTab === "overview" && (
                        <div className="grid grid-cols-2 gap-3 px-1">
                          <div className="bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md">
                            <div className="flex items-center mb-1">
                              <Clock className={`w-4 h-4 text-${plan.color} mr-1`} />
                              <span className="text-xs text-forex-light/90 font-medium">Duration</span>
                            </div>
                            <div className="text-forex-light font-medium">{plan.duration}</div>
                          </div>

                          <div className="bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md">
                            <div className="flex items-center mb-1">
                              <TrendingDown className={`w-4 h-4 text-${plan.color} mr-1`} />
                              <span className="text-xs text-forex-light/90 font-medium">Max Drawdown</span>
                            </div>
                            <div className="text-forex-light font-medium">{plan.maxDrawdown}</div>
                          </div>

                          <div className="bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md">
                            <div className="flex items-center mb-1">
                              <Percent className={`w-4 h-4 text-${plan.color} mr-1`} />
                              <span className="text-xs text-forex-light/90 font-medium">Max Risk/Trade</span>
                            </div>
                            <div className="text-forex-light font-medium">{plan.maxRiskPerTrade}</div>
                          </div>

                          <div className="bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md">
                            <div className="flex items-center mb-1">
                              <BarChart3 className={`w-4 h-4 text-${plan.color} mr-1`} />
                              <span className="text-xs text-forex-light/90 font-medium">Min Trades</span>
                            </div>
                            <div className="text-forex-light font-medium">{plan.minTrades.split(' ')[0]}</div>
                          </div>

                          <div className="bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md col-span-2">
                            <div className="flex items-center mb-1">
                              <Clock3 className={`w-4 h-4 text-${plan.color} mr-1`} />
                              <span className="text-xs text-forex-light/90 font-medium">Min Trade Duration</span>
                            </div>
                            <div className="text-forex-light font-medium">{plan.minTradeDuration}</div>
                          </div>
                        </div>
                      )}

                      {/* Rules Tab */}
                      {activeTab === "rules" && (
                        <ul className="space-y-3 px-2">
                          {plan.rules.map((rule, idx) => (
                            <li key={idx} className="flex items-start group bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md">
                              <div className={`w-6 h-6 rounded-full bg-${plan.color}/30 flex items-center justify-center mr-3 mt-0.5 group-hover:bg-${plan.color}/50 transition-colors`}>
                                {React.cloneElement(rule.icon, { className: `text-${plan.color}` })}
                              </div>
                              <div>
                                <div className="text-forex-light font-medium text-sm">{rule.name}</div>
                                <div className="text-forex-light/80 text-xs">{rule.description}</div>
                              </div>
                            </li>
                          ))}
                        </ul>
                      )}

                      {/* Benefits Tab */}
                      {activeTab === "benefits" && (
                        <ul className="space-y-3 px-2">
                          {plan.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-start group bg-forex-dark p-3 rounded-md border border-forex-border/20 hover:border-forex-border/40 transition-colors shadow-md">
                              <div className={`w-6 h-6 rounded-full bg-${plan.color}/30 flex items-center justify-center mr-3 mt-0.5 group-hover:bg-${plan.color}/50 transition-colors`}>
                                <Check className={`w-4 h-4 text-${plan.color}`} />
                              </div>
                              <span className="text-forex-light">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>

                  {/* CTA Section */}
                  <div className="space-y-4">
                    {status.enrollable && (
                      <div className="flex items-center justify-center text-sm text-forex-light mb-2 bg-forex-dark p-2 rounded-md border border-forex-border/20">
                        <Calendar className={`w-5 h-5 mr-2 text-${plan.color}`} />
                        <span>Next starts: <span className="font-medium">{formatNextDate(status.nextStart)}</span></span>
                      </div>
                    )}

                    <Link to={plan.ctaLink} className="block">
                      <Button
                        className={cn(
                          "w-full py-4 px-6 text-sm font-medium rounded-lg shadow-md transition-all duration-300 hover:shadow-xl hover:scale-[1.02]",
                          `bg-gradient-to-r ${planColor} hover:opacity-90 border border-forex-border/20`
                        )}
                      >
                        <span className="flex items-center justify-center">
                          <ArrowRight className={`w-4 h-4 mr-2 ${
                            plan.color === 'purple-500' ? 'text-indigo-300' :
                            plan.color === 'forex-accent' ? 'text-forex-primary' :
                            'text-forex-accent'
                          }`} />
                          {status.message || plan.ctaText}
                        </span>
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>


      </div>
    </section>
  );
};

export default PricingTable;
