/**
 * useApiErrorHandler Hook
 * @description Custom hook for handling API errors consistently across the application
 * @version 1.0.0
 * @status stable
 */

import { useState, useCallback } from 'react';
import { useToast } from '@/common/components/ui/use-toast';

interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

interface UseApiErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
}

/**
 * Custom hook for handling API errors consistently across the application
 * @param options - Options for error handling behavior
 * @returns Object with error state and handler functions
 */
function useApiErrorHandler(options: UseApiErrorHandlerOptions = {}) {
  const { showToast = true, logToConsole = true } = options;
  const { toast } = useToast();
  const [error, setError] = useState<ApiError | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);

  /**
   * Handle an API error
   * @param error - The error object
   * @param customMessage - Optional custom message to display
   */
  const handleError = useCallback((error: any, customMessage?: string) => {
    // Extract error details
    let errorMessage = customMessage || 'An unexpected error occurred';
    let errorCode = '';
    let errorStatus = 0;
    let errorDetails = null;

    // Handle different error formats
    if (error) {
      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error.response) {
        // Axios error format
        errorStatus = error.response.status;
        errorMessage = error.response.data?.message || error.message || errorMessage;
        errorCode = error.response.data?.code || '';
        errorDetails = error.response.data;
      } else if (error.message) {
        errorMessage = error.message;
        errorCode = error.code || '';
        errorStatus = error.status || 0;
        errorDetails = error.details || null;
      }
    }

    // Create standardized error object
    const apiError: ApiError = {
      message: errorMessage,
      code: errorCode,
      status: errorStatus,
      details: errorDetails
    };

    // Set error state
    setError(apiError);
    setHasError(true);

    // Log to console if enabled
    if (logToConsole) {
      console.error('API Error:', apiError);
      console.error('Original error:', error);
    }

    // Show toast if enabled
    if (showToast) {
      toast({
        title: errorCode ? `Error ${errorCode}` : 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }

    return apiError;
  }, [toast, showToast, logToConsole]);

  /**
   * Clear the current error state
   */
  const clearError = useCallback(() => {
    setError(null);
    setHasError(false);
  }, []);

  return {
    error,
    hasError,
    handleError,
    clearError
  };
}

export default useApiErrorHandler;
