/**
 * Error Handling Utilities
 * @description Utility functions for handling errors gracefully in the application
 * @version 1.0.0
 * @status stable
 */

import { toast } from 'sonner';

/**
 * Handle API errors gracefully
 * @param error - The error object
 * @param fallbackMessage - Fallback message to display if error doesn't have a message
 * @param showToast - Whether to show a toast notification
 * @returns The error message
 */
export const handleApiError = (
  error: any,
  fallbackMessage: string = 'An unexpected error occurred',
  showToast: boolean = true
): string => {
  // Extract error message
  let errorMessage = fallbackMessage;

  if (error) {
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else if (error.response) {
      // Axios error format
      errorMessage = error.response.data?.message || error.message || errorMessage;
    } else if (error.message) {
      errorMessage = error.message;
    }
  }

  // Log error to console
  console.error('API Error:', error);

  // Show toast if enabled
  if (showToast) {
    toast.error(errorMessage);
  }

  return errorMessage;
};

/**
 * Create a safe version of a function that catches errors
 * @param fn - The function to make safe
 * @param fallbackValue - Fallback value to return if function throws
 * @param errorHandler - Optional custom error handler
 * @returns A safe version of the function
 */
export function createSafeFunction<T, Args extends any[]>(
  fn: (...args: Args) => T,
  fallbackValue: T,
  errorHandler?: (error: any) => void
): (...args: Args) => T {
  return (...args: Args) => {
    try {
      return fn(...args);
    } catch (error) {
      if (errorHandler) {
        errorHandler(error);
      } else {
        console.error('Error in safe function:', error);
      }
      return fallbackValue;
    }
  };
}

/**
 * Safely access a nested property in an object
 * @param obj - The object to access
 * @param path - The path to the property (e.g. 'user.profile.name')
 * @param defaultValue - Default value to return if property doesn't exist
 * @returns The property value or default value
 */
export const safelyAccessProperty = <T>(
  obj: any,
  path: string,
  defaultValue: T
): T => {
  try {
    const keys = path.split('.');
    let result = obj;

    for (const key of keys) {
      if (result === undefined || result === null) {
        return defaultValue;
      }
      result = result[key];
    }

    return (result === undefined || result === null) ? defaultValue : result as T;
  } catch (error) {
    console.error(`Error accessing property ${path}:`, error);
    return defaultValue;
  }
};
