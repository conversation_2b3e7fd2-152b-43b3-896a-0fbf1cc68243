// Types for the Challenge Context

export interface Challenge {
  id: string;
  title: string;
  type: 'Daily' | 'Weekly' | 'Monthly';
  status: 'Active' | 'Upcoming' | 'Completed';
  endTime: string;
  progress: number;
  position: number;
  previousPosition?: number;
  performance: string;
  isPositive: boolean;
  accountId: string;
  rules: {
    maxDrawdown: number;
    currentDrawdown: number;
    profitTarget: number;
    currentProfit: number;
    tradingDays: number;
    daysCompleted: number;
  };
  leaderboard?: {
    topCompetitors: Competitor[];
    userPosition: number;
    totalParticipants: number;
  };
  metrics?: {
    winRate: string;
    tradesCompleted: number;
    averageTradeSize: string;
  };
}

export interface Competitor {
  id: string;
  name: string;
  position: number;
  performance: string;
}

export interface TradingAccount {
  id: string;
  name: string;
  balance: number;
  equity: number;
  connectedSince: string;
  isConnected: boolean;
  challengeIds: string[];
  usage: {
    activeChallenges: number;
    completedChallenges: number;
    upcomingChallenges: number;
    totalTrades: number;
    profitLoss: string;
  };
}

export interface PerformanceMetric {
  title: string;
  value: string;
  change: {
    value: string;
    positive: boolean;
    neutral?: boolean;
  };
  icon: React.ReactNode;
}

export interface WalletCredit {
  id: string;
  amount: string;
  expiryDate: string;
  source: string;
  sourceId: string;
  sourceType: 'challenge' | 'referral' | 'bonus';
  isExpired: boolean;
  isUsed: boolean;
}

export interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error';
  message: string;
  timestamp: string;
  read: boolean;
  challengeId?: string;
}

export interface TradeData {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  openTime: string;
  closeTime?: string;
  openPrice: number;
  closePrice?: number;
  volume: number;
  profit: number;
  pips: number;
  status: 'open' | 'closed';
  challengeId: string;
}
