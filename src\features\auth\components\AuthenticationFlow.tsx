/**
 * Authentication Flow Component
 * @description Component that handles the complete wallet authentication flow
 * @version 1.0.0
 * @status stable
 */

import React from 'react';
import { useAccount } from 'wagmi';
import { Button } from '@/common/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/common/components/ui/card';
import { WalletConnect } from './WalletConnect';
import { useSIWE } from '../hooks/useSIWE';
import { Loader2, Shield, Wallet } from 'lucide-react';

interface AuthenticationFlowProps {
  onAuthenticated?: () => void;
  className?: string;
}

export const AuthenticationFlow: React.FC<AuthenticationFlowProps> = ({
  onAuthenticated,
  className = '',
}) => {
  const { isConnected, address } = useAccount();
  const { signIn, needsSignIn, isLoading, isAuthenticated, user } = useSIWE();

  const handleSignIn = async () => {
    const success = await signIn();
    if (success && onAuthenticated) {
      onAuthenticated();
    }
  };

  // If user is already authenticated, show success state
  if (isAuthenticated && user) {
    return (
      <Card className={`w-full max-w-md ${className}`}>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
            <Shield className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <CardTitle className="text-xl">Welcome Back!</CardTitle>
          <CardDescription>
            You're signed in as {user.displayName}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-muted p-3">
            <p className="text-sm text-muted-foreground">Wallet Address:</p>
            <p className="font-mono text-sm">{user.walletAddress}</p>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>Role:</span>
            <span className="capitalize">{user.role}</span>
          </div>
          {user.verifiedHost && (
            <div className="flex items-center justify-center rounded-lg bg-blue-50 p-2 dark:bg-blue-900/20">
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                ✓ Verified Host
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // If wallet is connected but needs to sign in
  if (isConnected && needsSignIn) {
    return (
      <Card className={`w-full max-w-md ${className}`}>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
            <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <CardTitle className="text-xl">Sign In Required</CardTitle>
          <CardDescription>
            Please sign a message to authenticate with your wallet
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-muted p-3">
            <p className="text-sm text-muted-foreground">Connected Wallet:</p>
            <p className="font-mono text-sm">{address}</p>
          </div>
          <Button
            onClick={handleSignIn}
            disabled={isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing In...
              </>
            ) : (
              <>
                <Shield className="mr-2 h-4 w-4" />
                Sign In with Ethereum
              </>
            )}
          </Button>
          <p className="text-xs text-muted-foreground text-center">
            This will open your wallet to sign a message. No transaction fees required.
          </p>
        </CardContent>
      </Card>
    );
  }

  // If wallet is not connected, show connection flow
  return (
    <Card className={`w-full max-w-md ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900">
          <Wallet className="h-6 w-6 text-purple-600 dark:text-purple-400" />
        </div>
        <CardTitle className="text-xl">Connect Your Wallet</CardTitle>
        <CardDescription>
          Connect your wallet to access TradeChampionX
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <WalletConnect className="w-full" />
        <div className="space-y-2 text-xs text-muted-foreground">
          <p>• Secure wallet-based authentication</p>
          <p>• No passwords or email required</p>
          <p>• Full control of your data</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthenticationFlow;
