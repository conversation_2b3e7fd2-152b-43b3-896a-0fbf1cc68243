/**
 * Authentication Test Page
 * @description Simple page to test wallet authentication functionality
 * @version 1.0.0
 * @status testing
 */

import React from 'react';
import { useAccount } from 'wagmi';
import { useAuthStore } from '@/features/auth/stores/authStore';
import { useSIWE } from '@/features/auth/hooks/useSIWE';
import { AuthenticationFlow } from '@/features/auth/components/AuthenticationFlow';
import { WalletConnect } from '@/features/auth/components/WalletConnect';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Button } from '@/common/components/ui/button';
import { Badge } from '@/common/components/ui/badge';
import { Separator } from '@/common/components/ui/separator';

const AuthTest: React.FC = () => {
  const { address, isConnected, chain } = useAccount();
  const { user, isAuthenticated, sessionToken } = useAuthStore();
  const { signOut, isLoading } = useSIWE();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center py-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            🔐 Wallet Authentication Test
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Test the complete wallet authentication flow with SIWE
          </p>
        </div>

        {/* Authentication Flow */}
        <div className="flex justify-center">
          <AuthenticationFlow />
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Connection Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🔗 Connection Status
                <Badge variant={isConnected ? "default" : "secondary"}>
                  {isConnected ? "Connected" : "Disconnected"}
                </Badge>
              </CardTitle>
              <CardDescription>
                Wallet connection and network information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Wallet Address:</p>
                <p className="font-mono text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                  {address || 'Not connected'}
                </p>
              </div>
              {chain && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Network:</p>
                  <p className="text-sm">{chain.name} (ID: {chain.id})</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Authentication Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🛡️ Authentication Status
                <Badge variant={isAuthenticated ? "default" : "secondary"}>
                  {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                </Badge>
              </CardTitle>
              <CardDescription>
                SIWE authentication and user session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {user ? (
                <>
                  <div>
                    <p className="text-sm font-medium text-gray-500">User ID:</p>
                    <p className="font-mono text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
                      {user.id}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Display Name:</p>
                    <p className="text-sm">{user.displayName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Role:</p>
                    <Badge variant="outline">{user.role}</Badge>
                  </div>
                  {user.verifiedHost && (
                    <div>
                      <Badge variant="default">✓ Verified Host</Badge>
                    </div>
                  )}
                </>
              ) : (
                <p className="text-sm text-gray-500">No user data available</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Session Information */}
        {sessionToken && (
          <Card>
            <CardHeader>
              <CardTitle>🎫 Session Information</CardTitle>
              <CardDescription>
                JWT token and session details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div>
                <p className="text-sm font-medium text-gray-500 mb-2">JWT Token (first 50 chars):</p>
                <p className="font-mono text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded break-all">
                  {sessionToken.substring(0, 50)}...
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        {isAuthenticated && (
          <Card>
            <CardHeader>
              <CardTitle>⚡ Actions</CardTitle>
              <CardDescription>
                Test authentication actions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={signOut} 
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                {isLoading ? 'Signing Out...' : 'Sign Out'}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Backend Status */}
        <Card>
          <CardHeader>
            <CardTitle>🖥️ Backend Status</CardTitle>
            <CardDescription>
              Test backend API connectivity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm">
                <strong>Backend URL:</strong> http://localhost:5003
              </p>
              <p className="text-sm">
                <strong>Health Check:</strong> 
                <a 
                  href="http://localhost:5003/api/health" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 ml-2"
                >
                  Test API Health
                </a>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-semibold">1. Connect Wallet</h4>
              <p className="text-sm text-gray-600">Click "Connect Wallet" and choose your wallet (MetaMask recommended)</p>
            </div>
            <Separator />
            <div className="space-y-2">
              <h4 className="font-semibold">2. Sign Message</h4>
              <p className="text-sm text-gray-600">Click "Sign In with Ethereum" and approve the message in your wallet</p>
            </div>
            <Separator />
            <div className="space-y-2">
              <h4 className="font-semibold">3. Verify Authentication</h4>
              <p className="text-sm text-gray-600">Check that user data appears and session token is generated</p>
            </div>
            <Separator />
            <div className="space-y-2">
              <h4 className="font-semibold">4. Test Persistence</h4>
              <p className="text-sm text-gray-600">Refresh the page and verify you remain logged in</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthTest;
