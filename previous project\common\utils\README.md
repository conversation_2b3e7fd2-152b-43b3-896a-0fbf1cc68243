# Utilities

This directory contains utility functions that are used throughout the application. These utilities provide common functionality that is needed in multiple places.

## Available Utilities

### `cn` - Class Name Utility

The `cn` function combines multiple class names using clsx and tailwind-merge. It's useful for conditionally applying classes and merging class names from different sources.

```tsx
import { cn } from '@/common/utils';

// Basic usage
<div className={cn("text-red-500", "bg-blue-500")}>

// With conditional classes
<div className={cn(
  "base-class",
  isActive && "active-class",
  variant === "primary" ? "primary-class" : "secondary-class"
)}>
```

## Usage

Import utilities directly from the utils directory:

```tsx
import { cn } from '@/common/utils';
```

## Adding New Utilities

When adding new utility functions:

1. Add the function to the appropriate file or create a new file if needed
2. Export the function from the `index.ts` file
3. Add JSDoc comments to document the function's purpose, parameters, and return value
4. Include examples of how to use the function
5. Update this README.md file with information about the new utility

## Best Practices

- Keep utility functions pure and focused on a single responsibility
- Use TypeScript for type safety
- Write comprehensive JSDoc comments
- Include examples in the documentation
- Write unit tests for utility functions
