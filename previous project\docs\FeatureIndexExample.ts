/**
 * Trading Feature
 * 
 * This feature handles all trading-related functionality including:
 * - Trade execution
 * - Trade history
 * - Trade analytics
 * - Position management
 * 
 * @module features/trading
 */

// Export components
export { default as TradeHistory } from './components/TradeHistory';
export { default as TradeForm } from './components/TradeForm';
export { default as PositionCard } from './components/PositionCard';
export { default as TradeAnalytics } from './components/TradeAnalytics';

// Export hooks
export { useTradeHistory } from './hooks/useTradeHistory';
export { usePositions } from './hooks/usePositions';

// Export types
export type { Trade, Position, TradeStatus } from './types';

// Export services
export { tradeService } from './services/tradeService';

// Export constants
export { TRADE_TYPES, POSITION_STATUSES } from './constants';
