/**
 * Authentication Store
 * @description Zustand store for managing wallet-based authentication state
 * @version 1.0.0
 * @status stable
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  walletAddress: string;
  displayName?: string;
  role: string;
  verifiedHost: boolean;
  cumulativeROI: number;
  top3Finishes: number;
  badges?: any;
  signupDate: string;
  lastLoginAt?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  sessionToken: string | null;
  
  // Actions
  setUser: (user: User) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setLoading: (loading: boolean) => void;
  setSessionToken: (token: string | null) => void;
  logout: () => void;
  reset: () => void;
}

const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  sessionToken: null,
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setUser: (user: User) => {
        set({ user, isAuthenticated: true });
      },
      
      setAuthenticated: (authenticated: boolean) => {
        set({ isAuthenticated: authenticated });
      },
      
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
      
      setSessionToken: (token: string | null) => {
        set({ sessionToken: token });
      },
      
      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          sessionToken: null,
        });
      },
      
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        sessionToken: state.sessionToken,
      }),
    }
  )
);
