# Code Organization Guide

This document summarizes the organization principles and tools implemented in the TradeChampionX codebase to maintain readability and scalability.

## Key Organization Principles

### 1. Consistent Component Structure

All components follow a standardized structure:
- Imports (external then internal)
- Type definitions and interfaces
- Component JSDoc with status
- Component implementation
- Helper functions
- Exports

See `src/docs/ComponentTemplate.tsx` for a reference implementation.

### 2. Feature-Based Organization

The codebase is organized by features:
- Each feature has its own directory in `src/features/`
- Features contain components, hooks, services, and types
- Common utilities and components are in `src/common/`
- Pages compose features into complete views

### 3. Index Files for Clean Imports

Each feature directory has an index file that:
- Exports all public components, hooks, and utilities
- Provides documentation about the feature
- Enables clean imports from other parts of the application

See `src/docs/FeatureIndexExample.ts` for a reference implementation.

### 4. Component Status System

Components are tagged with status indicators:
- `@status experimental` - In development, API may change
- `@status beta` - Feature complete but not fully tested
- `@status stable` - Production ready
- `@status deprecated` - Scheduled for removal

See `src/docs/COMPONENT_STATUS.md` for details.

## Directory Structure

```
src/
├── common/               # Shared code used across features
│   ├── components/       # Reusable UI components
│   │   ├── layout/       # Layout components (Header, Footer, etc.)
│   │   ├── ui/           # UI components (Button, Input, etc.)
│   │   └── feedback/     # Feedback components (Toast, Alert, etc.)
│   ├── hooks/            # Custom React hooks
│   ├── services/         # API and service integrations
│   ├── utils/            # Utility functions
│   └── types/            # Shared TypeScript types
│
├── features/             # Feature-specific code
│   ├── auth/             # Authentication feature
│   ├── challenges/       # Trading challenges feature
│   ├── landing/          # Landing page feature
│   ├── leaderboard/      # Leaderboard feature
│   ├── profile/          # User profile feature
│   └── wallet/           # Wallet feature
│
├── pages/                # Page components that use features
├── contexts/             # React context providers
├── docs/                 # Documentation files
└── App.tsx               # Main application component
```

## Documentation Standards

- **README.md** files in each directory explain purpose and contents
- **JSDoc comments** on all components, hooks, and utilities
- **COMPONENT_STATUS.md** explains the component status system
- **STYLE_GUIDE.md** documents coding standards and best practices

## Best Practices

1. **Keep components focused** - Each component should do one thing well
2. **Maintain separation of concerns** - UI components shouldn't contain business logic
3. **Use TypeScript properly** - Define interfaces for all props and data structures
4. **Document as you code** - Add JSDoc comments when creating components
5. **Follow the style guide** - Consistent coding style improves readability
6. **Update status indicators** - Keep component status up to date as development progresses

## Tools and Resources

- **Style Guide**: `src/docs/STYLE_GUIDE.md`
- **Component Template**: `src/docs/ComponentTemplate.tsx`
- **Component Status System**: `src/docs/COMPONENT_STATUS.md`
- **Feature Index Example**: `src/docs/FeatureIndexExample.ts`
