import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from 'lucide-react';
import { Button } from '@/common/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/common/components/ui/card';
import './ErrorBoundary.css';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
  componentName?: string;
  containError?: boolean; // If true, error will be contained in a smaller UI rather than full screen
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error Boundary component to catch JavaScript errors in child components
 * and display a fallback UI instead of crashing the whole app
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console and potentially to an error reporting service
    console.error('Error caught by ErrorBoundary:', error);
    console.error('Component stack:', errorInfo.componentStack);

    this.setState({
      errorInfo
    });
  }

  handleReset = (): void => {
    // Reset the error boundary state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });

    // Call the onReset prop if provided
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback, componentName, containError } = this.props;

    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Use the styled card UI for contained errors
      if (containError) {
        return (
          <Card className="border-red-400 bg-forex-darker shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-red-500 flex items-center gap-2 text-base">
                <AlertTriangle className="h-4 w-4" />
                {componentName ? `Error in ${componentName}` : 'Component Error'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-forex-muted">
                {error?.message || 'An unexpected error occurred'}
              </div>
              {process.env.NODE_ENV === 'development' && error && (
                <div className="mt-2 p-2 bg-forex-dark rounded text-xs font-mono overflow-auto max-h-32">
                  {error.stack?.split('\n').slice(0, 3).join('\n')}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                size="sm"
                onClick={this.handleReset}
              >
                <RefreshCw className="h-3 w-3 mr-2" />
                Retry
              </Button>
            </CardFooter>
          </Card>
        );
      }

      // Use the full-screen error UI for critical errors
      return (
        <div className="error-boundary">
          <div className="error-container">
            <h2>
              <AlertTriangle className="inline-block mr-2" size={20} />
              {componentName ? `Error in ${componentName}` : 'Something went wrong'}
            </h2>
            <p>We're sorry, but there was an error loading this component.</p>
            <details>
              <summary>Error details</summary>
              <p>{error?.message}</p>
              {process.env.NODE_ENV === 'development' && (
                <pre className="error-stack">{error?.stack}</pre>
              )}
            </details>
            <button
              onClick={this.handleReset}
              className="retry-button"
            >
              <RefreshCw className="inline-block mr-1" size={14} />
              Try again
            </button>
          </div>
        </div>
      );
    }

    return children;
  }
}

export default ErrorBoundary;
