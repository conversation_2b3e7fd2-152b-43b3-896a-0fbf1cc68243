/**
 * Leaderboard Redux Slice
 * @description Redux slice for managing leaderboard state
 * @version 1.0.0
 * @status stable
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../../../app/store';
import { DetailedLeaderboardEntry, LeaderboardUpdatedPayload } from '../../../types/socketTypes';
import { leaderboardService } from '../services/leaderboardService';

// Define the initial state
interface LeaderboardState {
  entries: Record<number | 'global', DetailedLeaderboardEntry[]>; // Indexed by challengeId or 'global'
  loading: boolean;
  error: string | null;
  lastUpdated: Record<number | 'global', string>; // ISO timestamp indexed by challengeId or 'global'
  isRealtime: Record<number | 'global', boolean>; // Flag to indicate if the leaderboard is being updated in real-time
}

const initialState: LeaderboardState = {
  entries: {},
  loading: false,
  error: null,
  lastUpdated: {},
  isRealtime: {},
};

// Async thunk for fetching leaderboard data
export const fetchLeaderboard = createAsyncThunk(
  'leaderboard/fetchLeaderboard',
  async (challengeId: number, { rejectWithValue }) => {
    try {
      const data = await leaderboardService.getCurrentLeaderboard(challengeId);
      return { challengeId, data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch leaderboard');
    }
  }
);

// Async thunk for fetching global leaderboard data
export const fetchGlobalLeaderboard = createAsyncThunk(
  'leaderboard/fetchGlobalLeaderboard',
  async (limit: number = 50, { rejectWithValue }) => {
    try {
      const data = await leaderboardService.getGlobalLeaderboard(limit);
      return { data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch global leaderboard');
    }
  }
);

// Create the leaderboard slice
const leaderboardSlice = createSlice({
  name: 'leaderboard',
  initialState,
  reducers: {
    // Update leaderboard from socket event
    updateLeaderboard: (state, action: PayloadAction<LeaderboardUpdatedPayload>) => {
      const { challengeId, leaderboard, isDailySnapshot, snapshotTime } = action.payload;

      // Update the leaderboard entries
      state.entries[challengeId] = leaderboard;

      // Update the last updated timestamp
      state.lastUpdated[challengeId] = snapshotTime || new Date().toISOString();

      // Set the real-time flag
      state.isRealtime[challengeId] = !isDailySnapshot;
    },

    // Clear leaderboard data for a specific challenge
    clearLeaderboard: (state, action: PayloadAction<number>) => {
      const challengeId = action.payload;
      delete state.entries[challengeId];
      delete state.lastUpdated[challengeId];
      delete state.isRealtime[challengeId];
    },

    // Clear all leaderboard data
    clearAllLeaderboards: (state) => {
      state.entries = {};
      state.lastUpdated = {};
      state.isRealtime = {};
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLeaderboard.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLeaderboard.fulfilled, (state, action) => {
        const { challengeId, data } = action.payload;
        state.entries[challengeId] = data;
        state.lastUpdated[challengeId] = new Date().toISOString();
        state.loading = false;
      })
      .addCase(fetchLeaderboard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Global leaderboard cases
      .addCase(fetchGlobalLeaderboard.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGlobalLeaderboard.fulfilled, (state, action) => {
        const { data } = action.payload;
        state.entries['global'] = data;
        state.lastUpdated['global'] = new Date().toISOString();
        state.isRealtime['global'] = true;
        state.loading = false;
      })
      .addCase(fetchGlobalLeaderboard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { updateLeaderboard, clearLeaderboard, clearAllLeaderboards } = leaderboardSlice.actions;

// Export selectors
export const selectLeaderboard = (state: RootState, challengeId: number) =>
  state.leaderboard.entries[challengeId] || [];

export const selectGlobalLeaderboard = (state: RootState) =>
  state.leaderboard.entries['global'] || [];

export const selectLeaderboardLoading = (state: RootState) =>
  state.leaderboard.loading;

export const selectLeaderboardError = (state: RootState) =>
  state.leaderboard.error;

export const selectLeaderboardLastUpdated = (state: RootState, challengeId: number | 'global') =>
  state.leaderboard.lastUpdated[challengeId];

export const selectLeaderboardIsRealtime = (state: RootState, challengeId: number | 'global') =>
  state.leaderboard.isRealtime[challengeId];

// Export reducer
export default leaderboardSlice.reducer;
