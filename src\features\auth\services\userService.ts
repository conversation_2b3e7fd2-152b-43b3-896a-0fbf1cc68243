/**
 * User Service
 * @description Service for user-related operations with wallet authentication
 * @version 2.0.0
 * @status stable
 */

import { apiService } from "@/common/services/api";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAccount } from "wagmi";
import { useAuthStore } from "../stores/authStore";
import type { User } from "../stores/authStore";

/**
 * Hook for syncing user data with backend using wallet address
 * @returns Mutation for syncing user data
 */
export const useSyncUser = () => {
  const { address } = useAccount();
  const { setUser } = useAuthStore();

  return useMutation({
    mutationFn: async () => {
      if (!address) {
        throw new Error("No wallet address found");
      }

      const response = await apiService.post('/users/sync', {
        walletAddress: address,
      });

      const user: User = response.data;
      setUser(user);

      return user;
    },
  });
};

/**
 * Hook for getting user profile data
 * @returns Query for user profile
 */
export const useUserProfile = () => {
  const { address } = useAccount();
  const { user } = useAuthStore();

  return useQuery({
    queryKey: ['user-profile', address],
    queryFn: async () => {
      if (!address) {
        throw new Error("No wallet address found");
      }

      const response = await apiService.get(`/users/profile/${address}`);
      return response.data as User;
    },
    enabled: !!address,
    initialData: user,
  });
};
