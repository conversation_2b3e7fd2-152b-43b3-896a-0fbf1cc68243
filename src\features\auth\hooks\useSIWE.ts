/**
 * Sign-In with Ethereum Hook
 * @description Hook for SIWE authentication flow
 * @version 1.0.0
 * @status stable
 */

import { useCallback, useEffect } from 'react';
import { useAccount, useSignMessage } from 'wagmi';
import { SiweMessage } from 'siwe';
import { useAuthStore } from '../stores/authStore';
import { apiService } from '@/common/services/api';
import { toast } from 'sonner';

export const useSIWE = () => {
  const { address, isConnected } = useAccount();
  const { signMessageAsync } = useSignMessage();
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    setUser, 
    setAuthenticated, 
    setLoading, 
    setSessionToken,
    logout 
  } = useAuthStore();

  // Sign in with Ethereum
  const signIn = useCallback(async () => {
    if (!address || !isConnected) {
      toast.error('Please connect your wallet first');
      return false;
    }

    try {
      setLoading(true);

      // Step 1: Get nonce from backend
      const nonceResponse = await apiService.get('/auth/nonce');
      const nonce = nonceResponse.nonce;

      // Step 2: Create SIWE message
      const message = new SiweMessage({
        domain: window.location.host,
        address,
        statement: 'Sign in to TradeChampionX',
        uri: window.location.origin,
        version: '1',
        chainId: 1, // We'll use Ethereum mainnet for signing, regardless of current chain
        nonce,
        issuedAt: new Date().toISOString(),
      });

      // Step 3: Sign the message
      const signature = await signMessageAsync({
        message: message.prepareMessage(),
      });

      // Step 4: Verify signature with backend
      const verifyResponse = await apiService.post('/auth/verify', {
        message: message.prepareMessage(),
        signature,
        address,
      });

      const { user: userData, token } = verifyResponse;

      // Step 5: Update auth state
      setUser(userData);
      setSessionToken(token);
      setAuthenticated(true);

      toast.success('Successfully signed in!');
      return true;
    } catch (error: any) {
      console.error('SIWE sign in error:', error);
      toast.error(error.message || 'Failed to sign in');
      return false;
    } finally {
      setLoading(false);
    }
  }, [address, isConnected, signMessageAsync, setUser, setSessionToken, setAuthenticated, setLoading]);

  // Sign out
  const signOut = useCallback(async () => {
    try {
      // Call backend to invalidate session
      if (user?.id) {
        await apiService.post('/auth/logout');
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      logout();
      toast.success('Signed out successfully');
    }
  }, [user?.id, logout]);

  // Auto sign out when wallet disconnects
  useEffect(() => {
    if (!isConnected && isAuthenticated) {
      signOut();
    }
  }, [isConnected, isAuthenticated, signOut]);

  // Check if user needs to sign in (connected but not authenticated)
  const needsSignIn = isConnected && !isAuthenticated && !isLoading;

  return {
    signIn,
    signOut,
    needsSignIn,
    isLoading,
    isAuthenticated,
    user,
  };
};
