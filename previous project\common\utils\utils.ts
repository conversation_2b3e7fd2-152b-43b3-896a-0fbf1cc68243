import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines multiple class names using clsx and tailwind-merge
 *
 * This utility function combines multiple class names and resolves Tailwind CSS
 * conflicts by using tailwind-merge. It's useful for conditionally applying
 * classes and merging class names from different sources.
 *
 * @param {...ClassValue[]} inputs - Class names to combine
 * @returns {string} - Combined and merged class names
 *
 * @example
 * // Basic usage
 * <div className={cn("text-red-500", "bg-blue-500")}>
 *
 * @example
 * // With conditional classes
 * <div className={cn(
 *   "base-class",
 *   isActive && "active-class",
 *   variant === "primary" ? "primary-class" : "secondary-class"
 * )}>
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Checks if a color has sufficient contrast against a background color according to WCAG standards
 * 
 * @param foreground - Foreground color in hex format (e.g., "#FFFFFF")
 * @param background - Background color in hex format (e.g., "#000000")
 * @param wcagLevel - WCAG level to check against ("AA" or "AAA")
 * @returns boolean indicating if the contrast ratio meets the specified WCAG level
 */
export function hasEnoughContrast(foreground: string, background: string, wcagLevel: 'AA' | 'AAA' = 'AA'): boolean {
  // Convert hex colors to RGB
  const hexToRgb = (hex: string): { r: number; g: number; b: number } => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : { r: 0, g: 0, b: 0 };
  };

  // Calculate relative luminance
  const calculateLuminance = (rgb: { r: number; g: number; b: number }): number => {
    const sRGB = {
      r: rgb.r / 255,
      g: rgb.g / 255,
      b: rgb.b / 255,
    };

    const values = Object.values(sRGB).map((val) => {
      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);
    });

    return values[0] * 0.2126 + values[1] * 0.7152 + values[2] * 0.0722;
  };

  const fgRgb = hexToRgb(foreground);
  const bgRgb = hexToRgb(background);

  const fgLuminance = calculateLuminance(fgRgb);
  const bgLuminance = calculateLuminance(bgRgb);

  // Calculate contrast ratio
  const ratio =
    (Math.max(fgLuminance, bgLuminance) + 0.05) /
    (Math.min(fgLuminance, bgLuminance) + 0.05);

  // Check against WCAG standards
  const minimumRatio = wcagLevel === 'AAA' ? 7 : 4.5;
  return ratio >= minimumRatio;
}
