# Challenges Feature

This directory contains components and utilities related to trading challenges in TradeChampionX.

## Overview

The challenges feature allows users to:
- Browse available trading challenges
- View challenge details and rules
- Register for challenges
- Track challenge progress
- View challenge results

## Component Structure

### Challenge Listing
- **ChallengeList**: Displays a list of available challenges with filtering options
- **ChallengeCard**: Card component for displaying challenge summary information
- **ChallengeFilters**: Filters for challenge type, status, and date range

### Challenge Details
- **ChallengeDetails**: Detailed view of a specific challenge
- **ChallengeRules**: Displays the rules for a challenge
- **ChallengePrizePool**: Visualizes the prize pool distribution
- **ChallengeParticipants**: Shows participants in a challenge

### Challenge Entry
- **ChallengeEntryForm**: Form for entering a challenge
- **CTraderConnectionForm**: Form for connecting cTrader account
- **EntryConfirmation**: Confirmation screen after successful entry

### Challenge Progress
- **ChallengeProgress**: Displays user's progress in a challenge
- **TradeList**: Lists trades made during the challenge
- **PerformanceMetrics**: Shows key performance metrics (PnL, drawdown, etc.)
- **DisqualificationWarning**: Warning component for rule violations

## Data Models

### Challenge
```typescript
interface Challenge {
  id: number;
  type: 'daily' | 'weekly' | 'monthly';
  startDate: string;
  endDate: string;
  ruleset: ChallengeRuleset;
  prizePool: number;
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';
  _count?: {
    challengeEntries: number;
  };
}
```

### ChallengeEntry
```typescript
interface ChallengeEntry {
  id: number;
  userId: string;
  challengeId: number;
  ctraderAccountId: string;
  enrollmentTime: string;
  connectStatus: 'pending' | 'connected' | 'disconnected' | 'failed';
  disqualified: boolean;
  user?: {
    username: string;
  };
  challenge?: Challenge;
  trades?: Trade[];
}
```

## API Integration

The challenges feature integrates with the following API endpoints:

### Challenge Endpoints
- `GET /api/challenges` - Get all challenges with optional filters
- `GET /api/challenges/:id` - Get a specific challenge by ID
- `GET /api/challenges/default-ruleset/:type` - Get default ruleset for a challenge type

### Challenge Entry Endpoints
- `POST /api/challenge-entries` - Create a new challenge entry
- `GET /api/challenge-entries/me` - Get current user's challenge entries
- `GET /api/challenge-entries/:id` - Get a specific challenge entry
- `PUT /api/challenge-entries/:id/ctrader-tokens` - Update cTrader tokens

## Challenge Types

TradeChampionX offers three distinct challenge types:

1. **Daily Challenges**
   - 24-hour competition window
   - Lower entry fees and faster payouts
   - Perfect for day traders seeking quick results

2. **Weekly Challenges**
   - 7-day competition window
   - Balanced mix of time commitment and reward potential
   - Ideal for swing traders

3. **Monthly Challenges**
   - 30-day competition window
   - Largest prize pools and most flexible trading conditions
   - Designed for position traders

## Challenge Rules

All challenges follow transparent rules that are clearly communicated to participants:
- No hidden disqualification clauses
- No restrictive trading windows
- No manipulated spreads

Each challenge type has specific rules and requirements:

### Base Rules (All Challenge Types)
- Initial balance
- Maximum drawdown percentage
- Maximum risk per trade percentage
- No hedging allowed
- No martingale strategy allowed
- Minimum trade duration in minutes
- Minimum number of trades required

### Extended Rules (Weekly and Monthly Challenges)
- Maximum daily drawdown percentage
- Minimum swing trade days (optional)
- Minimum trading days (optional)

## Prize Distribution

75% of all entry fees are distributed back to the community through challenge prizes. The prize distribution is transparent and follows a predetermined structure based on performance metrics:

- Top performers receive a percentage of the prize pool
- Top 30% of participants receive wallet credits for future challenges

## Integration with cTrader

Challenges are integrated with cTrader for trade execution and performance tracking. This integration ensures accurate and reliable performance measurement:

1. Users connect their cTrader account during challenge entry
2. The system monitors trades via WebSocket connection
3. Trade data is used to calculate performance metrics
4. Rule violations trigger automatic disqualification

## Usage Examples

### Displaying Challenge List
```tsx
import { ChallengeList } from '@/features/challenges';

const ChallengesPage = () => {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Trading Challenges</h1>
      <ChallengeList />
    </div>
  );
};
```

### Displaying Challenge Details
```tsx
import { ChallengeDetails } from '@/features/challenges';

const ChallengePage = ({ challengeId }) => {
  return (
    <div className="container mx-auto py-8">
      <ChallengeDetails id={challengeId} />
    </div>
  );
};
```

### Entering a Challenge
```tsx
import { ChallengeEntryForm } from '@/features/challenges';

const EnterChallengePage = ({ challengeId }) => {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Enter Challenge</h1>
      <ChallengeEntryForm challengeId={challengeId} />
    </div>
  );
};
```

## Future Development

Planned enhancements for the challenges feature include:
- Challenge leaderboards
- Historical challenge results
- Challenge statistics and analytics
- Social sharing of challenge results
- Challenge templates for quick creation
- Advanced filtering options
