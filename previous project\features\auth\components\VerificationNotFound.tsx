import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/common/components/ui/button';
import { AlertTriangle } from 'lucide-react';

const VerificationNotFound = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-forex-dark p-4">
      <div className="w-full max-w-md bg-forex-card/80 backdrop-blur-md rounded-xl shadow-xl p-8 text-center border border-forex-border/20">
        <div className="mb-6">
          <div className="w-16 h-16 mx-auto flex items-center justify-center bg-red-500/20 rounded-full">
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>
        <h1 className="text-2xl font-bold text-white mb-4">Verification Failed</h1>
        <p className="text-forex-light/80 mb-6">
          We couldn't verify your account. The verification link may have expired or is invalid.
        </p>
        <div className="flex flex-col space-y-3">
          <Link to="/login">
            <Button className="w-full bg-forex-primary hover:bg-forex-primary/90 text-white">
              Return to Login
            </Button>
          </Link>
          <Link to="/">
            <Button variant="outline" className="w-full border-forex-border text-forex-light hover:bg-forex-dark/50">
              Go to Homepage
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VerificationNotFound;
