import React from 'react';
import { Button } from '@/common/components/ui/button';
import { Trophy, Users, Clock, DollarSign, TrendingUp, Crown } from 'lucide-react';

interface LiveChallengeCardProps {
  challenge: {
    id: string;
    name: string;
    hostName: string;
    hostBadge?: string;
    prizePool: number;
    participants: number;
    maxParticipants?: number;
    timeLeft: string;
    topROI: number;
    entryFee: number;
    bannerUrl?: string;
    status: 'live' | 'starting-soon' | 'ending-soon';
  };
  onJoin?: (challengeId: string) => void;
  onSpectate?: (challengeId: string) => void;
}

export const LiveChallengeCard: React.FC<LiveChallengeCardProps> = ({
  challenge,
  onJoin,
  onSpectate
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'live':
        return 'text-accent-emerald border-accent-emerald/30 bg-accent-emerald/10';
      case 'starting-soon':
        return 'text-accent-orange border-accent-orange/30 bg-accent-orange/10';
      case 'ending-soon':
        return 'text-accent-pink border-accent-pink/30 bg-accent-pink/10';
      default:
        return 'text-primary-400 border-primary-400/30 bg-primary-400/10';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'live':
        return 'LIVE';
      case 'starting-soon':
        return 'STARTING SOON';
      case 'ending-soon':
        return 'ENDING SOON';
      default:
        return 'LIVE';
    }
  };

  return (
    <div className="card-premium hover-lift group">
      {/* Challenge Banner */}
      {challenge.bannerUrl && (
        <div className="relative h-32 mb-4 rounded-lg overflow-hidden">
          <img 
            src={challenge.bannerUrl} 
            alt={challenge.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-bg-primary/80 to-transparent" />
        </div>
      )}

      {/* Status Badge */}
      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border mb-3 ${getStatusColor(challenge.status)}`}>
        <div className="w-2 h-2 rounded-full bg-current mr-2 animate-pulse" />
        {getStatusText(challenge.status)}
      </div>

      {/* Challenge Info */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-bold text-text-primary mb-1 group-hover:text-gradient transition-colors">
            {challenge.name}
          </h3>
          <div className="flex items-center gap-2 text-sm text-text-tertiary">
            <Crown className="w-4 h-4 text-accent-orange" />
            <span>Hosted by {challenge.hostName}</span>
            {challenge.hostBadge && (
              <span className="px-2 py-0.5 bg-accent-purple/20 text-accent-purple text-xs rounded-full">
                {challenge.hostBadge}
              </span>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-accent-emerald" />
              <span className="text-sm text-text-tertiary">Prize Pool</span>
            </div>
            <div className="text-xl font-bold text-accent-emerald">
              ${challenge.prizePool.toLocaleString()}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-primary-400" />
              <span className="text-sm text-text-tertiary">Participants</span>
            </div>
            <div className="text-xl font-bold text-primary-400">
              {challenge.participants}
              {challenge.maxParticipants && (
                <span className="text-sm text-text-tertiary font-normal">
                  /{challenge.maxParticipants}
                </span>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-accent-cyan" />
              <span className="text-sm text-text-tertiary">Time Left</span>
            </div>
            <div className="text-lg font-bold text-accent-cyan">
              {challenge.timeLeft}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-accent-orange" />
              <span className="text-sm text-text-tertiary">Top ROI</span>
            </div>
            <div className="text-lg font-bold text-accent-orange">
              +{challenge.topROI}%
            </div>
          </div>
        </div>

        {/* Entry Fee */}
        <div className="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
          <span className="text-sm text-text-tertiary">Entry Fee</span>
          <span className="text-lg font-bold text-text-primary">
            ${challenge.entryFee} USDC
          </span>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            onClick={() => onJoin?.(challenge.id)}
            className="btn-primary flex-1"
            size="sm"
          >
            <Trophy className="w-4 h-4 mr-2" />
            Join Challenge
          </Button>
          <Button
            onClick={() => onSpectate?.(challenge.id)}
            variant="outline"
            className="border-primary-500/50 text-primary-400 hover:bg-primary-500/10"
            size="sm"
          >
            <Users className="w-4 h-4 mr-2" />
            Watch
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LiveChallengeCard;
