# UI Components

This directory contains reusable UI components that are used throughout the application. These components provide consistent styling and behavior across the application.

## Component Categories

### Input Components

- **Input**: A styled input field for text entry
- **Button**: A customizable button component with various styles and sizes

### Navigation Components

- **Tabs**: A tabbed interface for organizing content
- **Carousel**: A component for displaying content in a scrollable carousel

### Feedback Components

- **Toast**: A notification component for displaying messages to the user
- **Toaster**: A container for toast notifications
- **Tooltip**: A component for displaying additional information on hover

### Display Components

- **Accordion**: A collapsible content component
- **Avatar**: A component for displaying user avatars
- **Badge**: A small label component for displaying status or counts

## Usage

All components are exported from the `index.ts` file, so you can import them directly from the UI components directory:

```tsx
import { Button, Input, Tabs, Toast } from '@/common/components/ui';
```

## Styling

These components use Tailwind CSS for styling and are designed to be consistent with the application's design system. The components are responsive and adapt to different screen sizes.

## Accessibility

All components are designed with accessibility in mind and follow best practices for keyboard navigation, screen reader support, and color contrast.

## Examples

### Button

```tsx
<Button variant="primary" size="medium" onClick={handleClick}>
  Click Me
</Button>
```

### Input

```tsx
<Input 
  type="text" 
  placeholder="Enter your name" 
  value={name} 
  onChange={handleNameChange} 
/>
```

### Tabs

```tsx
<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Content for Tab 1</TabsContent>
  <TabsContent value="tab2">Content for Tab 2</TabsContent>
</Tabs>
```
