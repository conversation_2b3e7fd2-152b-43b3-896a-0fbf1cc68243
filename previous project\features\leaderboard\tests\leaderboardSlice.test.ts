/**
 * Leaderboard Slice Tests
 * @description Tests for the leaderboard Redux slice
 * @version 1.0.0
 * @status stable
 */

import { configureStore } from '@reduxjs/toolkit';
import leaderboardReducer, {
  updateLeaderboard,
  clearLeaderboard,
  clearAllLeaderboards,
  selectLeaderboard,
  selectLeaderboardLastUpdated,
  selectLeaderboardIsRealtime
} from '../redux/leaderboardSlice';
import { LeaderboardUpdatedPayload } from '../../../types/socketTypes';

// Mock leaderboard data
const mockLeaderboardData = [
  {
    userId: 'user1',
    username: 'Trader1',
    rank: 1,
    score: 15.5,
    drawdown: 5.2,
    tradeCount: 25,
    winRate: 68.5,
    averagePnl: 0.62,
    change: 0
  },
  {
    userId: 'user2',
    username: 'Trader2',
    rank: 2,
    score: 12.3,
    drawdown: 6.8,
    tradeCount: 18,
    winRate: 55.5,
    averagePnl: 0.68,
    change: 1
  }
];

// Mock leaderboard updated payload
const mockLeaderboardUpdatedPayload: LeaderboardUpdatedPayload = {
  challengeId: 1,
  leaderboard: mockLeaderboardData,
  isDailySnapshot: false,
  snapshotTime: '2023-05-15T12:00:00Z'
};

describe('Leaderboard Slice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        leaderboard: leaderboardReducer
      }
    });
  });

  it('should handle initial state', () => {
    expect(selectLeaderboard(store.getState(), 1)).toEqual([]);
    expect(selectLeaderboardLastUpdated(store.getState(), 1)).toBeUndefined();
    expect(selectLeaderboardIsRealtime(store.getState(), 1)).toBeUndefined();
  });

  it('should handle updateLeaderboard', () => {
    store.dispatch(updateLeaderboard(mockLeaderboardUpdatedPayload));
    
    expect(selectLeaderboard(store.getState(), 1)).toEqual(mockLeaderboardData);
    expect(selectLeaderboardLastUpdated(store.getState(), 1)).toBe('2023-05-15T12:00:00Z');
    expect(selectLeaderboardIsRealtime(store.getState(), 1)).toBe(true);
  });

  it('should handle clearLeaderboard', () => {
    // First add some data
    store.dispatch(updateLeaderboard(mockLeaderboardUpdatedPayload));
    
    // Then clear it
    store.dispatch(clearLeaderboard(1));
    
    expect(selectLeaderboard(store.getState(), 1)).toEqual([]);
    expect(selectLeaderboardLastUpdated(store.getState(), 1)).toBeUndefined();
    expect(selectLeaderboardIsRealtime(store.getState(), 1)).toBeUndefined();
  });

  it('should handle clearAllLeaderboards', () => {
    // First add some data for multiple challenges
    store.dispatch(updateLeaderboard({
      ...mockLeaderboardUpdatedPayload,
      challengeId: 1
    }));
    
    store.dispatch(updateLeaderboard({
      ...mockLeaderboardUpdatedPayload,
      challengeId: 2
    }));
    
    // Then clear all
    store.dispatch(clearAllLeaderboards());
    
    expect(selectLeaderboard(store.getState(), 1)).toEqual([]);
    expect(selectLeaderboard(store.getState(), 2)).toEqual([]);
    expect(selectLeaderboardLastUpdated(store.getState(), 1)).toBeUndefined();
    expect(selectLeaderboardLastUpdated(store.getState(), 2)).toBeUndefined();
    expect(selectLeaderboardIsRealtime(store.getState(), 1)).toBeUndefined();
    expect(selectLeaderboardIsRealtime(store.getState(), 2)).toBeUndefined();
  });

  it('should handle daily snapshots correctly', () => {
    store.dispatch(updateLeaderboard({
      ...mockLeaderboardUpdatedPayload,
      isDailySnapshot: true
    }));
    
    expect(selectLeaderboardIsRealtime(store.getState(), 1)).toBe(false);
  });
});
