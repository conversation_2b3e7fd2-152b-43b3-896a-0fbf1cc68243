# Profile System

The profile section allows users to manage their personal information, security settings, and wallet functionality.

## Components

### Profile Page

The main profile page that combines all profile-related components.

**Usage:**
```tsx
<Profile />
```

### PersonalInfoCard

Displays and allows editing of user's personal information.

**Features:**
- Display user's avatar, username, email, and member since date
- Edit username functionality

**Usage:**
```tsx
<PersonalInfoCard user={user} backendUser={backendUser} />
```

### SecuritySettingsCard

Provides links to Clerk's security settings for password management and two-factor authentication.

**Features:**
- Manage password settings
- Enable/disable two-factor authentication

**Usage:**
```tsx
<SecuritySettingsCard />
```

### WalletInfoCard

Displays wallet balance and transaction history, and allows users to deposit funds.

**Features:**
- View current wallet balance
- Deposit funds to wallet
- View transaction history

**Usage:**
```tsx
<WalletInfoCard backendUser={backendUser} />
```

### CryptoAddressCard

Allows users to manage their cryptocurrency wallet address for prize payouts.

**Features:**
- Add, update, or delete crypto wallet address
- Copy address to clipboard

**Usage:**
```tsx
<CryptoAddressCard backendUser={backendUser} />
```

## API Integration

The profile components use the following API endpoints:

- `GET /api/users/me` - Get current user data
- `PUT /api/users/username` - Update username
- `PUT /api/users/crypto-address` - Update crypto address
- `GET /api/transactions` - Get transaction history
- `POST /api/transactions` - Create a new transaction

## Authentication

All profile functionality requires user authentication via Clerk. The profile page is protected using the `ProtectedRoute` component.

## Styling

The profile components use the common UI components from the `@/common/components/ui` directory for consistent styling.

## Future Improvements

- Add additional wallet management features like withdrawals
- Support for multiple cryptocurrency addresses
- Transaction filtering and pagination
- Enhanced security features like email confirmations for profile changes 