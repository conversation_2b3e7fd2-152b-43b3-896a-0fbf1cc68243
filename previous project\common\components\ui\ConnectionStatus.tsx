import React from 'react';
import { CheckCircle, AlertCircle, Loader2, Wifi, WifiOff } from 'lucide-react';

interface ConnectionStatusProps {
  status: 'connected' | 'connecting' | 'disconnected' | 'error';
  message?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  status,
  message,
  showIcon = true,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-3'
  };

  const iconSizes = {
    sm: 12,
    md: 16,
    lg: 20
  };

  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: CheckCircle,
          color: 'text-green-400',
          bg: 'bg-green-500/10',
          border: 'border-green-500/20',
          text: message || 'Connected',
          pulse: false
        };
      case 'connecting':
        return {
          icon: Loader2,
          color: 'text-blue-400',
          bg: 'bg-blue-500/10',
          border: 'border-blue-500/20',
          text: message || 'Connecting...',
          pulse: true
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          color: 'text-gray-400',
          bg: 'bg-gray-500/10',
          border: 'border-gray-500/20',
          text: message || 'Disconnected',
          pulse: false
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-400',
          bg: 'bg-red-500/10',
          border: 'border-red-500/20',
          text: message || 'Connection Error',
          pulse: false
        };
      default:
        return {
          icon: Wifi,
          color: 'text-gray-400',
          bg: 'bg-gray-500/10',
          border: 'border-gray-500/20',
          text: 'Unknown',
          pulse: false
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className={`
      inline-flex items-center space-x-2 rounded-lg border
      ${config.bg} ${config.border} ${sizeClasses[size]}
      transition-all duration-200
    `}>
      {showIcon && (
        <Icon 
          size={iconSizes[size]} 
          className={`
            ${config.color} 
            ${config.pulse ? 'animate-spin' : ''}
            ${status === 'connected' ? 'animate-pulse-slow' : ''}
          `} 
        />
      )}
      <span className={`font-medium ${config.color}`}>
        {config.text}
      </span>
    </div>
  );
};
