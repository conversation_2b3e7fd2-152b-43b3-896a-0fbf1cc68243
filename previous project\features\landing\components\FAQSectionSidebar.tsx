import React, { useState, useEffect } from "react";
import { Search, ChevronRight } from "lucide-react";
import { Input } from "@/common/components/ui/input";
import { cn } from "@/common/utils";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface Category {
  id: string;
  name: string;
  icon: string;
}

const FAQSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("general");
  const [activeQuestion, setActiveQuestion] = useState<string | null>(null);
  const [filteredFAQs, setFilteredFAQs] = useState<FAQItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Initialize with the first category's questions
  useEffect(() => {
    if (!isSearching) {
      setFilteredFAQs(faqs.filter(faq => faq.category === activeCategory));
    }
  }, [activeCategory, isSearching]);

  // Filter FAQs based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setIsSearching(false);
      setFilteredFAQs(faqs.filter(faq => faq.category === activeCategory));
      return;
    }

    setIsSearching(true);
    const query = searchQuery.toLowerCase();
    const filtered = faqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
    );
    setFilteredFAQs(filtered);
  }, [searchQuery, activeCategory]);

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setIsSearching(false);
  };

  // Get unique categories from FAQs
  const getUniqueCategories = () => {
    return categories;
  };

  return (
    <section className="py-10 bg-forex-dark">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-8">
          <span className="inline-block px-3 py-1 bg-forex-primary/20 text-forex-primary font-medium rounded-full mb-2">
            Support
          </span>
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
            Frequently Asked Questions
          </h2>
          <p className="text-sm text-forex-light/80 mb-6">
            Find answers to common questions about our trading challenges
          </p>

          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-4 w-4 text-forex-light/50" />
            </div>
            <Input
              type="search"
              placeholder="Search for answers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-2 bg-forex-card/30 border-forex-border/20 text-white focus:border-forex-primary focus:ring-forex-primary/20 rounded-lg text-sm"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-forex-light/50 hover:text-forex-primary"
              >
                <span className="text-xs">Clear</span>
              </button>
            )}
          </div>
        </div>

        {/* FAQ Content with Sidebar */}
        <div className="max-w-6xl mx-auto bg-forex-card/20 rounded-xl overflow-hidden border border-forex-border/20">
          <div className="flex flex-col md:flex-row">
            {/* Sidebar Navigation */}
            <div className="md:w-64 bg-forex-card/40 border-r border-forex-border/20">
              <nav className="p-4">
                <ul className="space-y-1">
                  {getUniqueCategories().map((category) => (
                    <li key={category.id}>
                      <button
                        onClick={() => {
                          setActiveCategory(category.id);
                          setIsSearching(false);
                          setSearchQuery("");
                        }}
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-lg flex items-center text-sm transition-colors",
                          activeCategory === category.id && !isSearching
                            ? "bg-forex-primary/20 text-forex-primary"
                            : "text-forex-light/80 hover:bg-forex-card/60 hover:text-white"
                        )}
                      >
                        <span className="mr-2">{category.icon}</span>
                        {category.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            {/* FAQ Content */}
            <div className="flex-1 p-6 md:p-8 max-h-[70vh] overflow-y-auto">
              {isSearching && (
                <div className="mb-6 pb-4 border-b border-forex-border/20">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <Search className="w-4 h-4 mr-2 text-forex-primary" />
                    Search Results {filteredFAQs.length > 0 ? `(${filteredFAQs.length})` : ''}
                  </h3>
                </div>
              )}

              {!isSearching && (
                <div className="mb-6 pb-4 border-b border-forex-border/20">
                  <h3 className="text-lg font-medium text-white">
                    {categories.find(c => c.id === activeCategory)?.name}
                  </h3>
                </div>
              )}

              {filteredFAQs.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-forex-light/80 mb-2">No results found for "{searchQuery}"</p>
                  <button
                    onClick={clearSearch}
                    className="text-forex-primary hover:text-forex-primary/80 text-sm"
                  >
                    Clear search
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredFAQs.map((faq) => (
                    <div
                      key={faq.id}
                      className="bg-forex-card/30 rounded-lg border border-forex-border/20 overflow-hidden"
                    >
                      <button
                        onClick={() => setActiveQuestion(activeQuestion === faq.id ? null : faq.id)}
                        className="w-full px-4 py-3 flex justify-between items-center text-left focus:outline-none"
                      >
                        <h4 className="text-sm font-medium text-white pr-4">
                          {faq.question}
                        </h4>
                        <ChevronRight
                          className={cn(
                            "h-4 w-4 text-forex-primary transition-transform",
                            activeQuestion === faq.id ? "rotate-90" : ""
                          )}
                        />
                      </button>
                      {activeQuestion === faq.id && (
                        <div className="px-4 py-3 border-t border-forex-border/10 bg-forex-card/40">
                          <p className="text-sm text-forex-light/80">
                            {faq.answer}
                          </p>
                          {isSearching && (
                            <div className="mt-2 pt-2 border-t border-forex-border/10 text-xs text-forex-light/60 flex items-center">
                              <span className="mr-1">Category:</span>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setActiveCategory(categories.find(c => c.name === faq.category)?.id || "general");
                                  setIsSearching(false);
                                  setSearchQuery("");
                                }}
                                className="text-forex-primary hover:underline"
                              >
                                {faq.category}
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Contact Support */}
        <div className="max-w-4xl mx-auto mt-8 text-center">
          <p className="text-forex-light/80 mb-3 text-sm">
            Can't find what you're looking for?
          </p>
          <a
            href="#"
            className="inline-block px-4 py-2 bg-gradient-to-r from-forex-primary to-forex-accent text-white rounded-lg hover:opacity-90 transition-opacity font-medium text-sm"
          >
            Contact Support
          </a>
        </div>
      </div>
    </section>
  );
};

// Categories
const categories: Category[] = [
  { id: "general", name: "General Information", icon: "📌" },
  { id: "challenges", name: "Trading Challenges", icon: "🏆" },
  { id: "prizes", name: "Prizes & Rewards", icon: "💰" },
  { id: "technical", name: "Technical", icon: "🔧" },
  { id: "rules", name: "Rules & Requirements", icon: "📋" },
  { id: "payments", name: "Payments & Accounts", icon: "💳" },
  { id: "community", name: "Community", icon: "👥" }
];

// FAQ data
const faqs: FAQItem[] = [
  {
    id: "faq-1",
    question: "What is TradeChampionX?",
    answer: "TradeChampionX is a trading challenge platform where traders compete for prizes. Unlike prop firms, we distribute 75% of entry fees directly to top performers with transparent rules and guaranteed payouts.",
    category: "General Information"
  },
  {
    id: "faq-2",
    question: "How do I get started?",
    answer: "Create an account, select a challenge, pay the entry fee, connect your cTrader account, and start trading. Our platform automatically tracks your performance.",
    category: "General Information"
  },
  {
    id: "faq-3",
    question: "What makes TradeChampionX different from prop firms?",
    answer: "We distribute 75% of entry fees to top performers, with transparent rules, no hidden disqualification clauses, no restrictive trading windows, and guaranteed payouts.",
    category: "General Information"
  },
  {
    id: "faq-4",
    question: "What types of challenges do you offer?",
    answer: "We offer daily challenges (quick results), weekly challenges (balanced time/reward), and monthly challenges (largest prize pools, flexible conditions).",
    category: "Trading Challenges"
  },
  {
    id: "faq-5",
    question: "How long do challenges last?",
    answer: "Daily challenges last 24 hours, weekly challenges last 7 days, and monthly challenges last 30 days.",
    category: "Trading Challenges"
  },
  {
    id: "faq-6",
    question: "Can I participate in multiple challenges simultaneously?",
    answer: "Yes. Each challenge is tracked separately, allowing you to test different strategies across timeframes and maximize earning potential.",
    category: "Trading Challenges"
  },
  {
    id: "faq-7",
    question: "How does the dynamic prize pool work?",
    answer: "75% of entry fees go to the community: 60% to top performers (1st, 2nd, 3rd) and 15% to wallet credits for top 30% of traders. Example: 100 participants at $20 each = $2,000 pool with $1,500 to community.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-8",
    question: "How are prizes distributed?",
    answer: "Daily: 50% to 1st, 30% to 2nd, 20% to 3rd. Weekly: 40% to 1st, 25% to 2nd, 15% to 3rd, 20% split among 4th-10th. Monthly: broader distribution with additional credits.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-9",
    question: "What are wallet credits?",
    answer: "Internal currency for top 30% performers. Use for future challenge entries or combine with crypto payments. Expire after 30 days of inactivity.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-10",
    question: "When do I receive my winnings?",
    answer: "Top 3 winners receive cryptocurrency payouts within 24 hours of challenge completion after admin review.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-11",
    question: "How do I connect my cTrader account?",
    answer: "After registration and payment, authorize our app through cTrader's OAuth. We'll sync your trading data in real-time throughout the challenge.",
    category: "Technical"
  },
  {
    id: "faq-12",
    question: "What if there's an issue with my cTrader connection?",
    answer: "We automatically attempt to refresh your access token. If problems persist, you'll receive Discord notifications and our support team will help resolve issues.",
    category: "Technical"
  },
  {
    id: "faq-13",
    question: "How often is the leaderboard updated?",
    answer: "In real-time whenever a trader closes a position, providing immediate tracking of your standing against other participants.",
    category: "Technical"
  },
  {
    id: "faq-14",
    question: "What happens if I exceed maximum drawdown?",
    answer: "Automatic disqualification if you exceed limits (4% daily, 6% weekly, 10% monthly). You'll receive Discord notification and leaderboard status update.",
    category: "Rules & Requirements"
  },
  {
    id: "faq-15",
    question: "Are there trading restrictions during challenges?",
    answer: "No restrictive requirements. Trade all major forex pairs, cryptocurrencies, indices, and commodities available on cTrader with no session restrictions.",
    category: "Rules & Requirements"
  },
  {
    id: "faq-16",
    question: "What are the minimum trading requirements?",
    answer: "Daily: 2+ trades. Weekly: 3+ trades. Monthly: 6+ different trading days OR 3+ swing trades held for 2+ days each.",
    category: "Rules & Requirements"
  },
  {
    id: "faq-17",
    question: "What payment methods do you accept?",
    answer: "Cryptocurrency payments through NOWPayments (Bitcoin, Ethereum, etc.). Secure process with quick confirmation.",
    category: "Payments & Accounts"
  },
  {
    id: "faq-18",
    question: "Can I use wallet credits for partial payments?",
    answer: "Yes. Combine credits with cryptocurrency. The system automatically applies available credits and charges only the remaining amount.",
    category: "Payments & Accounts"
  },
  {
    id: "faq-19",
    question: "How do I check my wallet balance?",
    answer: "Your wallet balance is displayed on your dashboard after logging in, showing transaction history and expiring credits.",
    category: "Payments & Accounts"
  },
  {
    id: "faq-20",
    question: "How do I connect my Discord account?",
    answer: "Follow instructions after registration. Once connected, you'll receive the 'Verified Challenger' role with access to exclusive channels.",
    category: "Community"
  },
  {
    id: "faq-21",
    question: "What benefits does the Discord community offer?",
    answer: "Real-time updates, trading discussions, direct support access, educational content, and exclusive announcements. Active members receive special perks.",
    category: "Community"
  },
  {
    id: "faq-22",
    question: "Are there community events?",
    answer: "Yes, we regularly host special events, webinars, and exclusive competitions for our community members.",
    category: "Community"
  }
];

export default FAQSection;
