import { Link } from "react-router-dom";
import Logo from "./Logo";
import { ArrowUp } from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "@/common/utils";

/**
 * Footer link item structure
 * @interface FooterLink
 */
interface FooterLink {
  /** Display text for the link */
  label: string;
  /** URL or anchor for the link */
  to: string;
  /** Whether the link points to an external site */
  isExternal: boolean;
}

/**
 * Footer navigation category structure
 * @interface FooterCategory
 */
interface FooterCategory {
  /** Category title */
  title: string;
  /** Links in this category */
  links: FooterLink[];
}

/**
 * Footer navigation categories for organization
 */
const footerNavigation: FooterCategory[] = [
  {
    title: "Platform",
    links: [
      { label: "Home", to: "/", isExternal: false },
      { label: "Leaderboard", to: "/leaderboard", isExternal: false },
      { label: "Challenges", to: "#pricing", isExternal: false },
      { label: "FAQ", to: "#faq", isExternal: false }
    ]
  },
  {
    title: "Resources",
    links: [
      { label: "How It Works", to: "#how-it-works", isExternal: false },
      { label: "Discord Community", to: "#discord", isExternal: false },
      { label: "Trading Rules", to: "#rules", isExternal: false }
    ]
  },
  {
    title: "Legal",
    links: [
      { label: "Terms of Service", to: "/terms", isExternal: false },
      { label: "Privacy Policy", to: "/privacy", isExternal: false }
    ]
  }
];

/**
 * Footer component for TradeChampionX
 *
 * Displays the site footer with navigation links organized by category,
 * logo, tagline, and copyright information. Also includes a "back to top"
 * button that appears when the user scrolls down the page.
 *
 * @component
 * @example
 * <Footer />
 */
const Footer = () => {
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Handle scroll for back to top button with throttling
  useEffect(() => {
    // Throttle function to limit scroll event processing
    const throttle = (callback: Function, delay: number) => {
      let lastCall = 0;
      return function() {
        const now = new Date().getTime();
        if (now - lastCall < delay) {
          return;
        }
        lastCall = now;
        callback();
      };
    };

    // Throttled scroll handler
    const handleScroll = throttle(() => {
      setShowBackToTop(window.scrollY > 300);
    }, 100); // Only run at most once every 100ms

    // Use passive: true for better scroll performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  /**
   * Scrolls the window to the top with smooth animation
   * Used by the "back to top" button
   */
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  return (
    <footer className="bg-forex-dark border-t border-forex-border/20 relative" role="contentinfo" aria-label="Site footer">
      {/* Back to Top Button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-6 right-6 z-30 bg-forex-primary text-white p-3 rounded-full shadow-lg hover:bg-forex-hover transition-all duration-300 transform",
          showBackToTop ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Back to top"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            scrollToTop();
          }
        }}
      >
        <ArrowUp className="w-5 h-5" aria-hidden="true" />
      </button>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Tagline */}
          <div className="flex flex-col items-center md:items-start">
            <Logo variant="white" size="medium" linkWrapper={false} />
            <p className="mt-4 text-forex-light/60 text-sm max-w-xs text-center md:text-left">
              Fair trading challenges with transparent rules and guaranteed payouts.
            </p>
          </div>

          {/* Navigation Categories */}
          {footerNavigation.map((category) => (
            <div key={category.title} className="flex flex-col items-center md:items-start">
              <h3 className="text-white font-semibold mb-4" id={`footer-${category.title.toLowerCase()}-heading`}>{category.title}</h3>
              <ul className="space-y-2" aria-labelledby={`footer-${category.title.toLowerCase()}-heading`}>
                {category.links.map((link) => (
                  <li key={link.label}>
                    {link.isExternal ? (
                      <a
                        href={link.to}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-forex-light/70 hover:text-forex-primary transition-colors text-sm"
                        aria-label={`${link.label} (opens in a new tab)`}
                      >
                        {link.label}
                      </a>
                    ) : link.to.startsWith('#') ? (
                      <a
                        href={link.to}
                        className="text-forex-light/70 hover:text-forex-primary transition-colors text-sm"
                        onClick={(e) => {
                          // Handle scroll for anchor links
                          const targetId = link.to.substring(1);
                          const targetElement = document.getElementById(targetId);
                          if (targetElement) {
                            e.preventDefault();
                            targetElement.scrollIntoView({ behavior: "smooth" });
                          }
                        }}
                      >
                        {link.label}
                      </a>
                    ) : (
                      <Link
                        to={link.to}
                        className="text-forex-light/70 hover:text-forex-primary transition-colors text-sm"
                      >
                        {link.label}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-12 pt-6 border-t border-forex-border/20 text-center">
          <p className="text-forex-light/50 text-sm">
            &copy; {new Date().getFullYear()} TradeChampionX. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
