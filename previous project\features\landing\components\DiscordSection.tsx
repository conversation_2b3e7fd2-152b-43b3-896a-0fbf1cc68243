
import React from 'react';
import { But<PERSON> } from '@/common/components/ui/button';
import { ArrowRight, MessageSquare, Users, Zap, Trophy, Bell } from 'lucide-react';

const DiscordSection = () => {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Dynamic background with animated elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#5865F2]/20 via-forex-dark to-forex-dark/95"></div>

      {/* Animated Discord-style particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-10 md:w-1.5 md:h-16 bg-[#5865F2] rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              opacity: 0.1 + Math.random() * 0.3,
              filter: 'blur(1px)',
              transform: 'rotate(30deg)',
              animation: `float ${8 + Math.random() * 15}s infinite ease-in-out`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section header with glow effect */}
        <div className="text-center mb-12" data-aos="fade-up">
          <div className="inline-flex items-center justify-center mb-4 px-4 py-1.5 rounded-full bg-[#5865F2]/10 border border-[#5865F2]/20">
            <svg className="w-5 h-5 text-[#5865F2] mr-2" viewBox="0 0 127.14 96.36" fill="currentColor">
              <path d="M107.7,8.07A105.15,105.15,0,0,0,81.47,0a72.06,72.06,0,0,0-3.36,6.83A97.68,97.68,0,0,0,49,6.83,72.37,72.37,0,0,0,45.64,0,105.89,105.89,0,0,0,19.39,8.09C2.79,32.65-1.71,56.6.54,80.21h0A105.73,105.73,0,0,0,32.71,96.36,77.7,77.7,0,0,0,39.6,85.25a68.42,68.42,0,0,1-10.85-5.18c.91-.66,1.8-1.34,2.66-2a75.57,75.57,0,0,0,64.32,0c.87.71,1.76,1.39,2.66,2a68.68,68.68,0,0,1-10.87,5.19,77,77,0,0,0,6.89,11.1A105.25,105.25,0,0,0,126.6,80.22h0C129.24,52.84,122.09,29.11,107.7,8.07ZM42.45,65.69C36.18,65.69,31,60,31,53s5-12.74,11.43-12.74S54,46,53.89,53,48.84,65.69,42.45,65.69Zm42.24,0C78.41,65.69,73.25,60,73.25,53s5-12.74,11.44-12.74S96.23,46,96.12,53,91.08,65.69,84.69,65.69Z"/>
            </svg>
            <span className="text-[#5865F2] font-medium">Discord Community</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold heading-primary mb-4 tracking-tight">
            Join the Ultimate <span className="text-[#5865F2]">Trading Network</span>
          </h2>
          <p className="text-lg paragraph-bright max-w-2xl mx-auto">
            Connect with serious traders, access exclusive resources, and elevate your trading game in our vibrant Discord community.
          </p>
        </div>

        {/* Main content with glass card effect */}
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Left side - Feature cards */}
            <div className="space-y-5" data-aos="fade-right" data-aos-delay="100">
              <div className="group p-6 rounded-xl bg-gradient-to-br from-forex-dark/80 to-forex-dark/60 backdrop-blur-md border border-[#5865F2]/10 hover:border-[#5865F2]/30 transition-all duration-300 hover:shadow-[0_0_15px_rgba(88,101,242,0.15)] overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#5865F2]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <div className="w-10 h-10 rounded-lg bg-[#5865F2]/10 flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 text-[#5865F2]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold feature-title mb-2 group-hover:text-[#5865F2] transition-colors">Exclusive Trading Channels</h3>
                    <p className="subtitle-light">Access specialized channels for different trading styles, markets, and strategies. Get insights from experienced traders and share your own analysis.</p>
                  </div>
                </div>
              </div>

              <div className="group p-6 rounded-xl bg-gradient-to-br from-forex-dark/80 to-forex-dark/60 backdrop-blur-md border border-[#5865F2]/10 hover:border-[#5865F2]/30 transition-all duration-300 hover:shadow-[0_0_15px_rgba(88,101,242,0.15)] overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#5865F2]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <div className="w-10 h-10 rounded-lg bg-[#5865F2]/10 flex items-center justify-center">
                      <Bell className="w-5 h-5 text-[#5865F2]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold feature-title mb-2 group-hover:text-[#5865F2] transition-colors">Real-time Alerts & Updates</h3>
                    <p className="subtitle-light">Get instant notifications about new challenges, leaderboard changes, and important platform updates directly in Discord.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Feature cards */}
            <div className="space-y-5" data-aos="fade-left" data-aos-delay="200">
              <div className="group p-6 rounded-xl bg-gradient-to-br from-forex-dark/80 to-forex-dark/60 backdrop-blur-md border border-[#5865F2]/10 hover:border-[#5865F2]/30 transition-all duration-300 hover:shadow-[0_0_15px_rgba(88,101,242,0.15)] overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#5865F2]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <div className="w-10 h-10 rounded-lg bg-[#5865F2]/10 flex items-center justify-center">
                      <Trophy className="w-5 h-5 text-[#5865F2]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold feature-title mb-2 group-hover:text-[#5865F2] transition-colors">Early Access to Challenges</h3>
                    <p className="subtitle-light">Discord members get first access to new trading challenges, special community events, and exclusive promotions before anyone else.</p>
                  </div>
                </div>
              </div>

              <div className="group p-6 rounded-xl bg-gradient-to-br from-forex-dark/80 to-forex-dark/60 backdrop-blur-md border border-[#5865F2]/10 hover:border-[#5865F2]/30 transition-all duration-300 hover:shadow-[0_0_15px_rgba(88,101,242,0.15)] overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-r from-[#5865F2]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <div className="w-10 h-10 rounded-lg bg-[#5865F2]/10 flex items-center justify-center">
                      <Users className="w-5 h-5 text-[#5865F2]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold feature-title mb-2 group-hover:text-[#5865F2] transition-colors">Direct Support & Networking</h3>
                    <p className="subtitle-light">Get direct access to our support team and connect with like-minded traders to share experiences, strategies, and build your trading network.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Button */}
          <div className="mt-12 text-center" data-aos="fade-up" data-aos-delay="300">
            <a href="https://discord.gg/tradechampionx" target="_blank" rel="noopener noreferrer" className="inline-block">
              <Button className="bg-[#5865F2] hover:bg-[#5865F2]/90 text-white px-8 py-6 text-lg font-semibold rounded-xl shadow-[0_0_20px_rgba(88,101,242,0.3)] hover:shadow-[0_0_25px_rgba(88,101,242,0.5)] transition-all duration-300 transform hover:-translate-y-1">
                Join Our Discord
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </a>
            <p className="mt-4 subtitle-light text-sm">No spam. Unsubscribe anytime.</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DiscordSection;
