# Error Handling System

This document outlines the error handling system implemented in the application to prevent errors in one component from crashing the entire application.

## Components

### 1. ErrorBoundary Component

The `ErrorBoundary` component is a class component that catches JavaScript errors anywhere in its child component tree and displays a fallback UI instead of crashing the entire application.

```tsx
import ErrorBoundary from '@/common/components/ErrorBoundary';

// Usage
<ErrorBoundary componentName="MyComponent" containError={true}>
  <MyComponent />
</ErrorBoundary>
```

**Props:**
- `children`: The components to render
- `fallback`: Optional custom fallback UI
- `onReset`: Optional callback function to reset the error state
- `componentName`: Optional name of the component for error reporting
- `containError`: If true, error will be contained in a smaller UI rather than full screen

### 2. AppErrorBoundary Component

The `AppErrorBoundary` component is a top-level error boundary for the entire application. It catches errors that occur anywhere in the application and displays a full-screen error UI.

```tsx
import AppErrorBoundary from '@/common/components/AppErrorBoundary';

// Usage
<AppErrorBoundary>
  <App />
</AppErrorBoundary>
```

### 3. withErrorBoundary HOC

The `withErrorBoundary` higher-order component wraps a component with an error boundary.

```tsx
import withErrorBoundary from '@/common/hoc/withErrorBoundary';

// Usage
const SafeComponent = withErrorBoundary(UnsafeComponent, {
  componentName: 'UnsafeComponent',
  containError: true
});
```

### 4. Error Handling Utilities

The `errorHandling.tsx` file contains utility functions for error handling:

```tsx
import { 
  withErrorBoundary, 
  formatErrorMessage, 
  isNetworkError, 
  getUserFriendlyErrorMessage 
} from '@/common/utils/errorHandling';

// Usage
const errorMessage = getUserFriendlyErrorMessage(error);
```

## API Error Handling

The application uses a custom `ApiError` class for better error handling:

```tsx
import { ApiError } from '@/common/services/api';

// Example of catching and handling API errors
try {
  const data = await apiService.getData();
} catch (error) {
  if (error instanceof ApiError) {
    console.error(`API Error ${error.status}: ${error.message}`);
  } else {
    console.error('Unknown error:', error);
  }
}
```

## Custom Hook for API Error Handling

The `useApiErrorHandler` hook provides a standardized way to handle API errors:

```tsx
import useApiErrorHandler from '@/common/hooks/useApiErrorHandler';

// Usage in a component
const { error, hasError, handleError, clearError } = useApiErrorHandler();

// Example of using the hook with an API call
const fetchData = async () => {
  try {
    const data = await apiService.getData();
    return data;
  } catch (error) {
    handleError(error);
    return null;
  }
};
```

## Best Practices

1. **Wrap Components with Error Boundaries**
   - Wrap individual components with error boundaries to prevent errors from propagating up the component tree
   - Use the `containError` prop to control the size of the error UI

2. **Use the AppErrorBoundary for Top-Level Errors**
   - The AppErrorBoundary should be used at the top level of the application

3. **Handle API Errors Properly**
   - Use the `useApiErrorHandler` hook for consistent API error handling
   - Display user-friendly error messages using `getUserFriendlyErrorMessage`

4. **Provide Reset Functionality**
   - Always provide a way for users to recover from errors
   - Use the `onReset` prop to define custom reset behavior

5. **Log Errors for Debugging**
   - Log errors to the console for debugging purposes
   - Consider implementing a more robust error logging system for production

## Example Implementation

```tsx
// Component with error boundary
const MyComponent = () => {
  const { error, hasError, handleError } = useApiErrorHandler();
  
  const fetchData = async () => {
    try {
      const data = await apiService.getData();
      // Process data
    } catch (error) {
      handleError(error, 'Failed to fetch data');
    }
  };
  
  return (
    <ErrorBoundary componentName="MyComponent" containError={true}>
      {hasError ? (
        <div className="error-message">{getUserFriendlyErrorMessage(error)}</div>
      ) : (
        // Component content
      )}
    </ErrorBoundary>
  );
};
```
