import React from 'react';
import { Navigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  adminOnly = false 
}) => {
  const { isLoaded, isSignedIn, user } = useUser();

  if (!isLoaded) {
    // Show loading state while Clerk loads
    return (
      <div className="min-h-screen flex items-center justify-center bg-forex-dark">
        <div className="w-12 h-12 border-4 border-forex-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!isSignedIn) {
    // Redirect to login if not signed in
    return <Navigate to="/login" replace />;
  }

  // If this is an admin-only route, check if the user is an admin
  if (adminOnly) {
    const isAdmin = user?.publicMetadata?.role === 'admin';
    
    if (!isAdmin) {
      // Redirect to home if not an admin
      return <Navigate to="/" replace />;
    }
  }

  // If all checks pass, render the children
  return <>{children}</>;
};

export default ProtectedRoute;
