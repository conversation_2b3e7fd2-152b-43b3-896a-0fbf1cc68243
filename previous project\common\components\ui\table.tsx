/**
 * Table Component
 * @description A responsive table component with various subcomponents
 * @version 1.0.0
 * @status stable
 */

import * as React from "react";
import { cn } from "@/lib/utils";

/**
 * Table component props
 */
interface TableProps extends React.HTMLAttributes<HTMLTableElement> {}

/**
 * Table component
 * @param props - Component props
 * @returns Table component
 */
const Table = React.forwardRef<HTMLTableElement, TableProps>(
  ({ className, ...props }, ref) => (
    <div className="w-full overflow-auto">
      <table
        ref={ref}
        className={cn("w-full caption-bottom text-sm", className)}
        {...props}
      />
    </div>
  )
);
Table.displayName = "Table";

/**
 * Table header props
 */
interface TableHeaderProps extends React.HTMLAttributes<HTMLTableSectionElement> {}

/**
 * Table header component
 * @param props - Component props
 * @returns Table header component
 */
const TableHeader = React.forwardRef<HTMLTableSectionElement, TableHeaderProps>(
  ({ className, ...props }, ref) => (
    <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
  )
);
TableHeader.displayName = "TableHeader";

/**
 * Table body props
 */
interface TableBodyProps extends React.HTMLAttributes<HTMLTableSectionElement> {}

/**
 * Table body component
 * @param props - Component props
 * @returns Table body component
 */
const TableBody = React.forwardRef<HTMLTableSectionElement, TableBodyProps>(
  ({ className, ...props }, ref) => (
    <tbody
      ref={ref}
      className={cn("[&_tr:last-child]:border-0", className)}
      {...props}
    />
  )
);
TableBody.displayName = "TableBody";

/**
 * Table footer props
 */
interface TableFooterProps extends React.HTMLAttributes<HTMLTableSectionElement> {}

/**
 * Table footer component
 * @param props - Component props
 * @returns Table footer component
 */
const TableFooter = React.forwardRef<HTMLTableSectionElement, TableFooterProps>(
  ({ className, ...props }, ref) => (
    <tfoot
      ref={ref}
      className={cn("bg-primary font-medium text-primary-foreground", className)}
      {...props}
    />
  )
);
TableFooter.displayName = "TableFooter";

/**
 * Table row props
 */
interface TableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {}

/**
 * Table row component
 * @param props - Component props
 * @returns Table row component
 */
const TableRow = React.forwardRef<HTMLTableRowElement, TableRowProps>(
  ({ className, ...props }, ref) => (
    <tr
      ref={ref}
      className={cn(
        "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
        className
      )}
      {...props}
    />
  )
);
TableRow.displayName = "TableRow";

/**
 * Table head props
 */
interface TableHeadProps extends React.ThHTMLAttributes<HTMLTableCellElement> {}

/**
 * Table head component
 * @param props - Component props
 * @returns Table head component
 */
const TableHead = React.forwardRef<HTMLTableCellElement, TableHeadProps>(
  ({ className, ...props }, ref) => (
    <th
      ref={ref}
      className={cn(
        "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
        className
      )}
      {...props}
    />
  )
);
TableHead.displayName = "TableHead";

/**
 * Table cell props
 */
interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {}

/**
 * Table cell component
 * @param props - Component props
 * @returns Table cell component
 */
const TableCell = React.forwardRef<HTMLTableCellElement, TableCellProps>(
  ({ className, ...props }, ref) => (
    <td
      ref={ref}
      className={cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className)}
      {...props}
    />
  )
);
TableCell.displayName = "TableCell";

/**
 * Table caption props
 */
interface TableCaptionProps extends React.HTMLAttributes<HTMLTableCaptionElement> {}

/**
 * Table caption component
 * @param props - Component props
 * @returns Table caption component
 */
const TableCaption = React.forwardRef<HTMLTableCaptionElement, TableCaptionProps>(
  ({ className, ...props }, ref) => (
    <caption
      ref={ref}
      className={cn("mt-4 text-sm text-muted-foreground", className)}
      {...props}
    />
  )
);
TableCaption.displayName = "TableCaption";

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
};
