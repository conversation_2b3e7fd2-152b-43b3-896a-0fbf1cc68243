import { useAuthStore } from '../stores/authStore';
import { useSIWE } from './useSIWE';
import { useDisconnect } from 'wagmi';

/**
 * Main authentication hook that provides a simple interface
 * for authentication state and actions
 */
export const useAuth = () => {
  const { 
    isAuthenticated, 
    user, 
    sessionToken, 
    setAuthenticated, 
    setUser, 
    setSessionToken 
  } = useAuthStore();

  const { signIn, loading: siweLoading } = useSIWE();
  const { disconnect } = useDisconnect();

  /**
   * Logout function - clears all auth state and disconnects wallet
   */
  const logout = () => {
    try {
      // Clear auth state
      setAuthenticated(false);
      setUser(null);
      setSessionToken(null);

      // Clear from localStorage
      localStorage.removeItem('auth-token');
      localStorage.removeItem('auth-user');

      // Disconnect wallet
      disconnect();

      console.log('User logged out successfully');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  /**
   * Login function - uses SIWE to authenticate
   */
  const login = async (address: string) => {
    try {
      const success = await signIn(address);
      return success;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  /**
   * Check if user has a specific role
   */
  const hasRole = (role: string) => {
    return user?.role === role;
  };

  /**
   * Check if user is admin
   */
  const isAdmin = () => {
    return hasRole('admin');
  };

  /**
   * Get user display name
   */
  const getDisplayName = () => {
    if (user?.displayName) {
      return user.displayName;
    }
    if (user?.walletAddress) {
      return `${user.walletAddress.slice(0, 6)}...${user.walletAddress.slice(-4)}`;
    }
    return 'Unknown User';
  };

  return {
    // State
    isAuthenticated,
    user,
    sessionToken,
    loading: siweLoading,
    
    // Actions
    login,
    logout,
    
    // Utilities
    hasRole,
    isAdmin,
    getDisplayName,
  };
};
