/**
 * ChallengeBrowser component for TradeChampionX
 *
 * This component displays available challenges for users to join, with filtering options
 * and detailed information about each challenge.
 *
 * @version 1.0.0
 * @status stable
 */

import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Skeleton
} from '@/common/components/ui';
import {
  Trophy,
  DollarSign,
  Users,
  Clock,
  ArrowRight,
  AlertTriangle,
  RefreshCw,
  Shield
} from 'lucide-react';
import { apiService } from '@/common/services/api';
import { formatCurrency, formatDate } from '@/common/utils/formatters';

/**
 * Challenge ruleset interface
 */
interface ChallengeRuleset {
  initialBalance: number;
  maxDrawdownPercent: number;
  maxRiskPerTradePercent: number;
  noHedging: boolean;
  noMartingale: boolean;
  minTradeDurationMinutes: number;
  minTrades: number;
  maxDailyDrawdownPercent?: number;
  minSwingTradeDays?: number;
  minTradingDays?: number;
}

/**
 * Challenge interface
 */
interface Challenge {
  id: number;
  type: string;
  status: string;
  startDate: string;
  endDate: string;
  prizePool: number;
  ruleset: ChallengeRuleset;
  entryFee: number;
  _count?: {
    challengeEntries?: number;
  };
}

/**
 * Challenge type badge component
 * @param type - Challenge type
 * @returns Challenge type badge
 */
const ChallengeTypeBadge = ({ type }: { type: string }) => {
  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'daily':
        return 'bg-forex-success/10 text-forex-success border-forex-success/20';
      case 'weekly':
        return 'bg-forex-primary/10 text-forex-primary border-forex-primary/20';
      case 'monthly':
        return 'bg-forex-accent/10 text-forex-accent border-forex-accent/20';
      default:
        return 'bg-forex-muted/10 text-forex-muted border-forex-muted/20';
    }
  };

  return (
    <Badge
      variant="outline"
      className={`${getTypeColor(type)} font-medium`}
    >
      {type.charAt(0).toUpperCase() + type.slice(1)}
    </Badge>
  );
};

/**
 * ChallengeBrowser component
 * @returns ChallengeBrowser component
 */
const ChallengeBrowser = () => {
  const navigate = useNavigate();
  // Fetch challenges with improved error handling and logging
  const { data: challenges = [], isLoading, error } = useQuery<Challenge[]>({
    queryKey: ['challenges', { status: 'upcoming,active' }],
    queryFn: async () => {
      try {
        console.log('Fetching challenges from API...');
        // First try with both upcoming and active status
        const data = await apiService.getChallenges({ status: 'upcoming,active' });
        console.log('Challenges API response (upcoming,active):', data);

        if (data && data.length === 0) {
          // If no challenges found, try with just 'upcoming' status
          console.log('No challenges found with upcoming,active status. Trying with just upcoming...');
          const upcomingData = await apiService.getChallenges({ status: 'upcoming' });
          console.log('Challenges API response (upcoming only):', upcomingData);

          if (upcomingData && upcomingData.length === 0) {
            // If still no challenges, try with no status filter to get all challenges
            console.log('No challenges found with upcoming status. Trying with no status filter...');
            const allData = await apiService.getChallenges({});
            console.log('Challenges API response (no status filter):', allData);
            return allData;
          }

          return upcomingData;
        }

        return data;
      } catch (error) {
        console.error('Error fetching challenges:', error);
        throw error;
      }
    },
    retry: 1,
    refetchOnWindowFocus: true,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Handle challenge entry
  const handleEnterChallenge = (challengeId: number) => {
    navigate(`/challenge-entries/new?challengeId=${challengeId}`);
  };

  // Display debug info in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('ChallengeBrowser rendered with challenges:', challenges);
    }
  }, [challenges]);

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-forex-light mb-2 tracking-tight">
            <span className="text-glow-blue">Trading</span>{' '}
            <span className="bg-clip-text text-transparent bg-gradient-primary animate-gradient-x">
              Challenges
            </span>
          </h1>
          <p className="text-forex-neutral">
            Join a challenge, compete with other traders, and win prizes based on your performance.
          </p>
        </div>
      </div>



      {/* Challenge Cards */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="premium-card relative overflow-hidden transition-all duration-300 group-hover:shadow-premium-blue">
              <CardHeader>
                <Skeleton className="h-6 w-24 mb-2" />
                <Skeleton className="h-8 w-full" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12 bg-red-500/10 border border-red-500/30 rounded-lg p-6">
          <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-forex-light mb-2">Error Loading Challenges</h3>
          <p className="text-forex-muted mb-4">
            There was an error loading the challenges. This might be due to a connection issue or the server might be down.
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      ) : challenges.length === 0 ? (
        <div className="text-center py-12">
          <Trophy className="h-16 w-16 text-forex-muted mx-auto mb-4 opacity-20" />
          <h3 className="text-xl font-medium text-forex-light mb-2">No Challenges Available</h3>
          <p className="text-forex-muted">
            There are no challenges available at the moment.
            Please check back later.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {challenges.map((challenge, index) => (
            <Card
              key={challenge.id}
              className="premium-card relative overflow-hidden transition-all duration-300 hover:shadow-premium-blue hover:-translate-y-1"
              style={{ animationDelay: `${(index + 1) * 100}ms` }}
            >
              <div className="absolute inset-0 bg-shimmer-gradient opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-shimmer"></div>
              <CardHeader className="relative z-10">
                <div className="flex justify-between items-start">
                  <ChallengeTypeBadge type={challenge.type} />
                  <Badge variant={challenge.status === 'active' ? 'default' : 'secondary'}>
                    {challenge.status === 'active' ? 'Active' : 'Upcoming'}
                  </Badge>
                </div>
                <CardTitle className="text-xl font-bold text-forex-light mt-2">
                  {challenge.type.charAt(0).toUpperCase() + challenge.type.slice(1)} Challenge
                </CardTitle>
                <CardDescription>
                  {challenge.status === 'active'
                    ? `Ends on ${formatDate(challenge.endDate)}`
                    : `Starts on ${formatDate(challenge.startDate)}`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="space-y-4">
                  <div className="flex items-center text-forex-light">
                    <DollarSign className="h-5 w-5 text-forex-primary mr-2" />
                    <span className="font-medium">Prize Pool:</span>
                    <span className="ml-auto font-bold">{formatCurrency(challenge.prizePool)}</span>
                  </div>
                  <div className="flex items-center text-forex-light">
                    <Users className="h-5 w-5 text-forex-primary mr-2" />
                    <span className="font-medium">Participants:</span>
                    <span className="ml-auto font-bold">{challenge._count?.challengeEntries || 0}</span>
                  </div>
                  <div className="flex items-center text-forex-light">
                    <Clock className="h-5 w-5 text-forex-primary mr-2" />
                    <span className="font-medium">Duration:</span>
                    <span className="ml-auto font-bold">
                      {challenge.type === 'daily' ? '24 hours' :
                       challenge.type === 'weekly' ? '7 days' : '30 days'}
                    </span>
                  </div>

                  {/* Challenge Rules Section */}
                  <div className="mt-4 pt-4 border-t border-forex-border">
                    <h4 className="text-sm font-semibold text-forex-light mb-2 flex items-center">
                      <Shield className="h-4 w-4 text-forex-primary mr-2" />
                      Challenge Rules
                    </h4>
                    <div className="space-y-2 text-sm">
                      {challenge.ruleset && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-forex-muted">Initial Balance:</span>
                            <span className="text-forex-light font-medium">{formatCurrency(challenge.ruleset.initialBalance)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-forex-muted">Max Drawdown:</span>
                            <span className="text-forex-light font-medium">{challenge.ruleset.maxDrawdownPercent}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-forex-muted">Max Risk Per Trade:</span>
                            <span className="text-forex-light font-medium">{challenge.ruleset.maxRiskPerTradePercent}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-forex-muted">Min Trades:</span>
                            <span className="text-forex-light font-medium">{challenge.ruleset.minTrades}</span>
                          </div>
                          {challenge.ruleset.maxDailyDrawdownPercent && (
                            <div className="flex justify-between">
                              <span className="text-forex-muted">Max Daily Drawdown:</span>
                              <span className="text-forex-light font-medium">{challenge.ruleset.maxDailyDrawdownPercent}%</span>
                            </div>
                          )}
                          {challenge.ruleset.minTradingDays && (
                            <div className="flex justify-between">
                              <span className="text-forex-muted">Min Trading Days:</span>
                              <span className="text-forex-light font-medium">{challenge.ruleset.minTradingDays}</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="relative z-10">
                <Button
                  className="w-full relative overflow-hidden group/btn bg-forex-darker hover:bg-forex-hover border border-forex-border text-forex-light font-medium shadow-premium-blue transition-all duration-300 ease-out"
                  onClick={() => handleEnterChallenge(challenge.id)}
                >
                  <span className="relative z-10 flex items-center">
                    Join Challenge
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </span>
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-forex-primary to-forex-accent opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300 ease-out"></span>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChallengeBrowser;
