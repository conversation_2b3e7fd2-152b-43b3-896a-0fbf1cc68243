import React, { useEffect, useState } from 'react';
import { But<PERSON> } from './ui/Button';
import { 
  Trophy, 
  Shield, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Crown, 
  ChevronRight,
  Eye,
  CheckCircle,
  ArrowRight,
  BarChart3,
  Clock,
  Zap,
  Target,
  Star,
  Globe,
  Lock,
  Coins,
  Play,
  ChevronDown
} from 'lucide-react';

const LandingPage = () => {
  const [stats, setStats] = useState({
    totalPrizePool: 847250,
    activeChallenges: 23,
    totalTraders: 5847,
    hostEarnings: 125000,
    avgPayout: 2850
  });

  // Animate stats on mount
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalPrizePool: prev.totalPrizePool + Math.floor(Math.random() * 500),
        totalTraders: prev.totalTraders + Math.floor(Math.random() * 3),
        hostEarnings: prev.hostEarnings + Math.floor(Math.random() * 200)
      }));
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-900/20 via-accent-purple/10 to-accent-cyan/10 pointer-events-none"></div>
        
        {/* Floating elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 border border-primary-500/30 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-accent-emerald/30 rounded-full animate-float"></div>
          <div className="absolute bottom-40 left-1/4 w-16 h-16 border border-accent-pink/30 rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-1/3 w-20 h-20 border border-accent-orange/30 rounded-full animate-float"></div>
        </div>

        {/* Trading chart animation */}
        <div className="absolute bottom-0 left-0 right-0 h-24 opacity-20">
          <svg width="100%" height="100%" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path
              d="M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
              fill="url(#gradientChart)"
            >
              <animate
                attributeName="d"
                dur="20s"
                repeatCount="indefinite"
                values="
                  M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z;
                  M0,80 C150,120 350,60 500,100 C650,140 800,60 1000,100 C1100,130 1200,100 1200,100 L1200,200 L0,200 Z;
                  M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
              />
            </path>
            <defs>
              <linearGradient id="gradientChart" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#0284c7" />
                <stop offset="100%" stopColor="#0ea5e9" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Live Stats Bar */}
          <div className="max-w-5xl mx-auto mb-12">
            <div className="glass-card p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">
                    ${stats.totalPrizePool.toLocaleString()}
                  </div>
                  <div className="text-sm text-tertiary">Total Prize Pool</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-emerald mb-1">
                    {stats.activeChallenges}
                  </div>
                  <div className="text-sm text-tertiary">Live Challenges</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-cyan mb-1">
                    {stats.totalTraders.toLocaleString()}
                  </div>
                  <div className="text-sm text-tertiary">Active Traders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-orange mb-1">
                    ${stats.hostEarnings.toLocaleString()}
                  </div>
                  <div className="text-sm text-tertiary">Host Earnings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-purple mb-1">
                    ${stats.avgPayout.toLocaleString()}
                  </div>
                  <div className="text-sm text-tertiary">Avg Payout</div>
                </div>
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto text-center">
            {/* Badge */}
            <div className="glass-card inline-block py-2 px-4 rounded-full mb-6 border border-primary-500/30">
              <span className="text-primary-400 text-sm font-medium flex items-center gap-2">
                <Trophy className="w-4 h-4" />
                Influencer-Hosted Trading Competitions • On-Chain Escrow • Live Payouts
              </span>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-primary mb-6 leading-tight">
              Trade. Compete.{" "}
              <span className="text-gradient">
                Dominate.
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-secondary mb-12 max-w-3xl mx-auto">
              Join influencer-hosted trading challenges with real market data. 
              Compete for USDC prizes secured in on-chain escrow. 
              <span className="text-accent-emerald font-semibold">No wallet? Watch live!</span>
            </p>

            {/* Multiple CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <Button
                size="lg"
                className="btn-primary text-white font-semibold text-base px-8 py-6 shadow-lg hover:scale-105 transition-all duration-300"
              >
                <Trophy className="mr-2 h-5 w-5" />
                Join Live Challenges
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="border-primary-500/50 text-primary-400 hover:bg-primary-500/10 hover:border-primary-400 font-semibold text-base px-8 py-6 hover:scale-105 transition-all duration-300"
              >
                <Crown className="mr-2 h-5 w-5" />
                Become a Host
              </Button>

              <Button
                size="lg"
                variant="ghost"
                className="text-secondary hover:text-primary hover:bg-white/5 font-medium text-base px-6 py-6 hover:scale-105 transition-all duration-300"
              >
                <Eye className="mr-2 h-5 w-5" />
                Watch Live
              </Button>
            </div>

            {/* Key Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card-modern text-center hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-600 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-primary mb-2">On-Chain Escrow</h3>
                <p className="text-sm text-tertiary">
                  Entry fees locked in smart contracts. Transparent prize pools with automatic payouts.
                </p>
              </div>

              <div className="card-modern text-center hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-r from-accent-cyan to-accent-emerald rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-primary mb-2">Live Market Data</h3>
                <p className="text-sm text-tertiary">
                  Paper trade with real-time market data. Server-authoritative fills ensure fairness.
                </p>
              </div>

              <div className="card-modern text-center hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-r from-accent-pink to-accent-orange rounded-full flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-primary mb-2">USDC Prizes</h3>
                <p className="text-sm text-tertiary">
                  Win real USDC paid directly to your wallet. No delays, no middlemen.
                </p>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-tertiary text-sm">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-accent-emerald" />
                <span>Base L2 Network</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-primary-400" />
                <span>Licensed Market Data</span>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-accent-cyan" />
                <span>Spectator Mode Available</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem/Solution Section */}
      <section className="py-24 bg-slate-900/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
                Why Traditional Prop Firms <span className="text-red-400">Fail Traders</span>
              </h2>
              <p className="text-xl text-secondary max-w-3xl mx-auto">
                Break free from the broken system. TradeChampionX offers true transparency and fairness.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Problems */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-red-400 mb-6">Traditional Prop Firms</h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-sm">✕</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Hidden Fees & Conditions</h4>
                      <p className="text-sm text-tertiary">Complex fee structures, withdrawal restrictions, and unclear payout conditions</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-sm">✕</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Delayed Payouts</h4>
                      <p className="text-sm text-tertiary">Weeks or months to receive winnings, if at all</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-sm">✕</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Opaque Operations</h4>
                      <p className="text-sm text-tertiary">No visibility into where your money goes or how prizes are distributed</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-sm">✕</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Unfair Advantages</h4>
                      <p className="text-sm text-tertiary">Insider information and preferential treatment for select traders</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Solutions */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-accent-emerald mb-6">TradeChampionX Solution</h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-4 p-4 bg-accent-emerald/10 border border-accent-emerald/20 rounded-lg">
                    <div className="w-6 h-6 bg-accent-emerald rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">100% Transparent Fees</h4>
                      <p className="text-sm text-tertiary">Fixed 10% platform fee. Host rake clearly displayed. No hidden costs.</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-accent-emerald/10 border border-accent-emerald/20 rounded-lg">
                    <div className="w-6 h-6 bg-accent-emerald rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Instant On-Chain Payouts</h4>
                      <p className="text-sm text-tertiary">USDC prizes paid directly to your wallet within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-accent-emerald/10 border border-accent-emerald/20 rounded-lg">
                    <div className="w-6 h-6 bg-accent-emerald rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Full Transparency</h4>
                      <p className="text-sm text-tertiary">View escrow contracts, prize pools, and all transactions on-chain</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-accent-emerald/10 border border-accent-emerald/20 rounded-lg">
                    <div className="w-6 h-6 bg-accent-emerald rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">Level Playing Field</h4>
                      <p className="text-sm text-tertiary">Server-authoritative fills and licensed market data ensure fairness</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 bg-gradient-to-b from-slate-800 to-slate-900">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
                How It Works
              </h2>
              <p className="text-xl text-secondary max-w-3xl mx-auto">
                Simple, transparent, and fair. Join challenges in 3 easy steps.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className="text-center">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-gradient-to-r from-primary-600 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Eye className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent-emerald rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">1</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-primary mb-4">Browse & Spectate</h3>
                <p className="text-secondary mb-6">
                  Explore live challenges hosted by your favorite influencers. Watch leaderboards and prize pools grow in real-time. No wallet required!
                </p>
                <div className="glass-card p-4">
                  <p className="text-sm text-tertiary">
                    <span className="text-accent-emerald font-semibold">Spectator Mode:</span> Full access to all challenges and leaderboards
                  </p>
                </div>
              </div>

              {/* Step 2 */}
              <div className="text-center">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-gradient-to-r from-accent-cyan to-accent-emerald rounded-full flex items-center justify-center mx-auto mb-4">
                    <Coins className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent-emerald rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">2</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-primary mb-4">Connect & Pay Entry</h3>
                <p className="text-secondary mb-6">
                  Connect your wallet and pay the entry fee in USDC. Funds are immediately locked in transparent on-chain escrow contracts.
                </p>
                <div className="glass-card p-4">
                  <p className="text-sm text-tertiary">
                    <span className="text-primary-400 font-semibold">Base L2:</span> Low fees, fast transactions
                  </p>
                </div>
              </div>

              {/* Step 3 */}
              <div className="text-center">
                <div className="relative mb-8">
                  <div className="w-20 h-20 bg-gradient-to-r from-accent-pink to-accent-orange rounded-full flex items-center justify-center mx-auto mb-4">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-accent-emerald rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">3</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-primary mb-4">Trade & Win</h3>
                <p className="text-secondary mb-6">
                  Paper trade with real market data. Climb the leaderboard and win USDC prizes paid directly to your wallet within 24 hours.
                </p>
                <div className="glass-card p-4">
                  <p className="text-sm text-tertiary">
                    <span className="text-accent-orange font-semibold">Fair Trading:</span> Server-authoritative fills, no manipulation
                  </p>
                </div>
              </div>
            </div>

            {/* CTA */}
            <div className="text-center mt-16">
              <Button
                size="lg"
                className="btn-primary text-white font-semibold text-lg px-10 py-6 shadow-lg hover:scale-105 transition-all duration-300"
              >
                <Play className="mr-2 h-5 w-5" />
                Start Your First Challenge
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Showcase */}
      <section className="py-24 bg-slate-950">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
                Built for the <span className="text-gradient">Crypto Generation</span>
              </h2>
              <p className="text-xl text-secondary max-w-3xl mx-auto">
                Every feature designed with transparency, fairness, and community in mind.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
              {/* Influencer Arenas */}
              <div className="order-2 lg:order-1">
                <div className="glass-card p-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-accent-purple to-accent-pink rounded-full flex items-center justify-center mb-6">
                    <Crown className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-4">Influencer-Hosted Arenas</h3>
                  <p className="text-secondary mb-6">
                    Each challenge feels like your favorite creator's personal battleground. Custom banners,
                    personal messages, and unique branding make every competition memorable.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Custom host branding and messaging</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Host rake options (0%, 10%, 20%)</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Social media integration</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative">
                  <div className="glass-card p-6 transform rotate-3 hover:rotate-0 transition-transform duration-300">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-accent-purple to-accent-pink rounded-full flex items-center justify-center">
                        <Crown className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-bold text-white">CryptoKing's Arena</h4>
                        <p className="text-sm text-tertiary">Daily Challenge • $5,000 Pool</p>
                      </div>
                    </div>
                    <p className="text-sm text-secondary italic mb-4">
                      "Welcome to my arena! Show me your best trades and win big. May the best trader win! 🚀"
                    </p>
                    <div className="flex justify-between text-sm">
                      <span className="text-tertiary">247 Participants</span>
                      <span className="text-accent-emerald">Live Now</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
              {/* On-Chain Transparency */}
              <div>
                <div className="relative">
                  <div className="glass-card p-6">
                    <h4 className="font-bold text-white mb-4">Prize Pool Breakdown</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-tertiary">Total Pool</span>
                        <span className="text-white font-semibold">$10,000 USDC</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-tertiary">Platform Fee (10%)</span>
                        <span className="text-red-400">-$1,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-tertiary">Host Rake (10%)</span>
                        <span className="text-accent-orange">-$900</span>
                      </div>
                      <div className="border-t border-white/10 pt-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-semibold text-white">Winner Pool</span>
                          <span className="text-accent-emerald font-bold">$8,100 USDC</span>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 p-3 bg-primary-500/10 border border-primary-500/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-primary-400" />
                        <span className="text-xs text-primary-400">View on Etherscan</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div className="glass-card p-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-primary-600 to-primary-500 rounded-full flex items-center justify-center mb-6">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-4">Complete Transparency</h3>
                  <p className="text-secondary mb-6">
                    Every transaction is on-chain and verifiable. See exactly where your entry fee goes,
                    how prizes are distributed, and track all payouts in real-time.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Smart contract escrow</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Real-time prize pool tracking</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Etherscan integration</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Live Leaderboards */}
              <div className="order-2 lg:order-1">
                <div className="glass-card p-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-accent-cyan to-accent-emerald rounded-full flex items-center justify-center mb-6">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-4">Live Leaderboards & Stats</h3>
                  <p className="text-secondary mb-6">
                    Watch the competition unfold in real-time. Track your performance, see competitor moves,
                    and build your reputation with every challenge.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Real-time ROI tracking</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Trader badges and achievements</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-emerald" />
                      <span className="text-sm text-tertiary">Historical performance data</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative">
                  <div className="glass-card p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-bold text-white">Live Leaderboard</h4>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-accent-emerald rounded-full animate-pulse"></div>
                        <span className="text-xs text-accent-emerald">LIVE</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-accent-emerald/10 border border-accent-emerald/20 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-accent-emerald rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">1</span>
                          </div>
                          <span className="text-white font-semibold">CryptoWizard</span>
                        </div>
                        <span className="text-accent-emerald font-bold">+47.3%</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">2</span>
                          </div>
                          <span className="text-white">TradeKing</span>
                        </div>
                        <span className="text-white">+31.8%</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-accent-orange rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">3</span>
                          </div>
                          <span className="text-white">DiamondHands</span>
                        </div>
                        <span className="text-white">+28.5%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing/Economics Section */}
      <section className="py-24 bg-gradient-to-b from-slate-900 to-slate-800">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
                Transparent <span className="text-gradient">Economics</span>
              </h2>
              <p className="text-xl text-secondary max-w-3xl mx-auto">
                No hidden fees, no surprises. See exactly how every dollar flows through the system.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
              {/* Platform Fee */}
              <div className="glass-card p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-600 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-white font-bold text-xl">10%</span>
                </div>
                <h3 className="text-xl font-bold text-primary mb-4">Platform Fee</h3>
                <p className="text-secondary mb-6">
                  Fixed 10% fee from the total prize pool. Used for platform development, security, and operations.
                </p>
                <div className="space-y-2 text-sm text-tertiary">
                  <div>• Server infrastructure</div>
                  <div>• Market data licensing</div>
                  <div>• Security audits</div>
                  <div>• Platform development</div>
                </div>
              </div>

              {/* Host Rake */}
              <div className="glass-card p-8 text-center border-2 border-accent-emerald/30">
                <div className="w-16 h-16 bg-gradient-to-r from-accent-emerald to-accent-cyan rounded-full flex items-center justify-center mx-auto mb-6">
                  <Crown className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-primary mb-4">Host Rake</h3>
                <p className="text-secondary mb-6">
                  Hosts choose 0%, 10%, or 20% rake from the net pool. Incentivizes quality content and community building.
                </p>
                <div className="space-y-2 text-sm text-tertiary">
                  <div>• 0% - Community focused</div>
                  <div>• 10% - Balanced approach</div>
                  <div>• 20% - Premium content</div>
                  <div>• Transparent selection</div>
                </div>
              </div>

              {/* Winner Pool */}
              <div className="glass-card p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-accent-orange to-accent-pink rounded-full flex items-center justify-center mx-auto mb-6">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-primary mb-4">Winner Pool</h3>
                <p className="text-secondary mb-6">
                  Remaining funds go directly to winners. Multiple payout structures: Winner Takes All, Top 3, Top 5.
                </p>
                <div className="space-y-2 text-sm text-tertiary">
                  <div>• Winner Takes All: 100%</div>
                  <div>• Top 3: 70/20/10%</div>
                  <div>• Top 5: 40/25/15/10/10%</div>
                  <div>• Instant USDC payouts</div>
                </div>
              </div>
            </div>

            {/* Example Breakdown */}
            <div className="glass-card p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-primary mb-6 text-center">Example: $10,000 Challenge</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-white mb-4">Fee Breakdown</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-white/5 rounded-lg">
                      <span className="text-tertiary">Total Entry Fees</span>
                      <span className="text-white font-semibold">$10,000</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-red-500/10 rounded-lg">
                      <span className="text-tertiary">Platform Fee (10%)</span>
                      <span className="text-red-400">-$1,000</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-accent-orange/10 rounded-lg">
                      <span className="text-tertiary">Host Rake (10%)</span>
                      <span className="text-accent-orange">-$900</span>
                    </div>
                    <div className="border-t border-white/10 pt-3">
                      <div className="flex justify-between items-center p-3 bg-accent-emerald/10 rounded-lg">
                        <span className="text-white font-semibold">Winner Pool</span>
                        <span className="text-accent-emerald font-bold">$8,100</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-4">Top 3 Payout (70/20/10%)</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-accent-emerald/10 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-accent-emerald rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">1</span>
                        </div>
                        <span className="text-white">1st Place</span>
                      </div>
                      <span className="text-accent-emerald font-bold">$5,670</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">2</span>
                        </div>
                        <span className="text-white">2nd Place</span>
                      </div>
                      <span className="text-white font-semibold">$1,620</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-accent-orange/10 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-accent-orange rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">3</span>
                        </div>
                        <span className="text-white">3rd Place</span>
                      </div>
                      <span className="text-accent-orange font-semibold">$810</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-slate-950">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-secondary">
                Everything you need to know about TradeChampionX
              </p>
            </div>

            <div className="space-y-6">
              {/* FAQ Item 1 */}
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  How are trades executed fairly?
                </h3>
                <p className="text-secondary">
                  All trades are executed server-side using licensed market data from professional providers.
                  Orders are filled at the next available tick after submission, ensuring no manipulation or
                  preferential treatment. The system is completely deterministic and auditable.
                </p>
              </div>

              {/* FAQ Item 2 */}
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  When do I receive my winnings?
                </h3>
                <p className="text-secondary">
                  Winners receive USDC payouts directly to their wallets within 24 hours of challenge completion.
                  There's a 24-hour dispute window, after which payouts are automatically processed on-chain.
                  No waiting weeks or months like traditional prop firms.
                </p>
              </div>

              {/* FAQ Item 3 */}
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  What happens if I don't have a wallet?
                </h3>
                <p className="text-secondary">
                  You can browse all challenges, view leaderboards, and watch competitions live without connecting
                  a wallet. Spectator mode gives you full access to the platform. You only need a wallet to
                  participate in challenges and receive winnings.
                </p>
              </div>

              {/* FAQ Item 4 */}
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  How do refunds work?
                </h3>
                <p className="text-secondary">
                  Refunds are only available if a challenge doesn't meet the minimum participant requirement or
                  if an admin cancels before start time. Once a challenge begins, no refunds are available -
                  this ensures fair competition for all participants.
                </p>
              </div>

              {/* FAQ Item 5 */}
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  Is this gambling or skill-based?
                </h3>
                <p className="text-secondary">
                  TradeChampionX operates as a skill-based contest platform. Success depends on trading knowledge,
                  market analysis, and risk management skills. All rules are transparent, and the platform
                  complies with applicable regulations for skill-based competitions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-24 bg-gradient-to-br from-primary-900/20 via-accent-purple/10 to-accent-cyan/10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl font-bold text-primary mb-6">
              Ready to <span className="text-gradient">Dominate</span>?
            </h2>
            <p className="text-xl text-secondary mb-12 max-w-2xl mx-auto">
              Join thousands of traders competing in the most transparent and fair trading challenges ever created.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <Button
                size="lg"
                className="btn-primary text-white font-semibold text-lg px-12 py-8 shadow-xl hover:scale-105 transition-all duration-300"
              >
                <Trophy className="mr-3 h-6 w-6" />
                Join Your First Challenge
                <ChevronRight className="ml-3 h-6 w-6" />
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="border-primary-500/50 text-primary-400 hover:bg-primary-500/10 hover:border-primary-400 font-semibold text-lg px-12 py-8 hover:scale-105 transition-all duration-300"
              >
                <Eye className="mr-3 h-6 w-6" />
                Watch Live Challenges
              </Button>
            </div>

            {/* Final Trust Indicators */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-accent-emerald mb-2">$847K+</div>
                <div className="text-sm text-tertiary">Total Prizes Paid</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-accent-cyan mb-2">5,847</div>
                <div className="text-sm text-tertiary">Active Traders</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-accent-orange mb-2">23</div>
                <div className="text-sm text-tertiary">Live Challenges</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-accent-purple mb-2">24h</div>
                <div className="text-sm text-tertiary">Avg Payout Time</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
