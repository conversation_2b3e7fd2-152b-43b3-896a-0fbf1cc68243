/**
 * Textarea Component
 * @description A textarea component for multi-line text input
 * @version 1.0.0
 * @status stable
 */

import * as React from "react"

import { cn } from "@/common/utils"

/**
 * Textarea component
 * @component
 * @example
 * <Textarea placeholder="Type your message here." />
 * 
 * @example
 * <Textarea 
 *   value={text} 
 *   onChange={(e) => setText(e.target.value)} 
 *   placeholder="Type your message here." 
 * />
 */
const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        "flex min-h-[80px] w-full rounded-md border border-forex-border/30 bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-forex-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      ref={ref}
      {...props}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
