/* Global text visibility fixes */

/* Fix for text-forex-light with opacity */
.text-forex-light\/10,
.text-forex-light\/20,
.text-forex-light\/30,
.text-forex-light\/40,
.text-forex-light\/50,
.text-forex-light\/60,
.text-forex-light\/70,
.text-forex-light\/80,
.text-forex-light\/90 {
  color: #ffffff !important;
}

/* Fix for text-forex-neutral with opacity */
.text-forex-neutral\/10,
.text-forex-neutral\/20,
.text-forex-neutral\/30,
.text-forex-neutral\/40,
.text-forex-neutral\/50,
.text-forex-neutral\/60,
.text-forex-neutral\/70,
.text-forex-neutral\/80,
.text-forex-neutral\/90 {
  color: #ffffff !important;
}

/* Fix for dark text on dark backgrounds */
[class*="bg-forex-dark"] [class*="text-forex-light"],
[class*="bg-forex-dark"] [class*="text-forex-neutral"],
[class*="bg-forex-card"] [class*="text-forex-light"],
[class*="bg-forex-card"] [class*="text-forex-neutral"] {
  color: #ffffff !important;
}

/* Add text shadow for better visibility */
.text-shadow-fix {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Force white text for specific components */
.faq-category-text,
.faq-question-text,
.faq-answer-text,
.sidebar-text,
.footer-text,
.hero-text,
.feature-text,
.pricing-text,
.testimonial-text {
  color: #ffffff !important;
}

/* Fix for specific opacity variants that might be used */
[class*="text-white\/"] {
  color: #ffffff !important;
}

/* Fix for specific opacity variants that might be used */
[class*="text-forex-light\/"] {
  color: #ffffff !important;
}

/* Fix for specific opacity variants that might be used */
[class*="text-forex-neutral\/"] {
  color: #ffffff !important;
}

/* Remove underlines from logo links */
a.no-underline,
a.no-underline:hover,
a.no-underline:focus,
a.no-underline:active {
  text-decoration: none !important;
}

/* Specifically target logo links */
a:has(img),
a:has(svg) {
  text-decoration: none !important;
}
