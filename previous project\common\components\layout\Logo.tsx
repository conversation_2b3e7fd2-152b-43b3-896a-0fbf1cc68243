import React from 'react';
import { Link } from 'react-router-dom';

interface LogoProps {
  variant?: 'default' | 'white';
  showText?: boolean;
  size?: 'small' | 'medium' | 'large';
  linkWrapper?: boolean;
}

const Logo: React.FC<LogoProps> = ({
  variant = 'default',
  showText = true,
  size = 'medium',
  linkWrapper = true
}) => {
  const sizeClasses = {
    small: { logo: "h-8 w-8", text: "text-lg" },
    medium: { logo: "h-10 w-10", text: "text-xl" },
    large: { logo: "h-14 w-14", text: "text-2xl" }
  };

  const textColorClass = variant === 'white'
    ? "text-white"
    : "bg-gradient-to-r from-blue-600 to-blue-500 bg-clip-text text-transparent";

  const LogoContent = () => (
    <div className="flex items-center space-x-2">
      <div className={`${sizeClasses[size].logo} rounded-full bg-gradient-to-br from-blue-600 to-blue-500 flex items-center justify-center text-white font-bold shadow-lg`}>
        <span>TX</span>
      </div>
      {showText && (
        <span className={`${sizeClasses[size].text} font-bold ${textColorClass} ml-1`}>
          TradeChampionX
        </span>
      )}
    </div>
  );

  return linkWrapper ? (
    <Link to="/" className="no-underline">
      <LogoContent />
    </Link>
  ) : (
    <LogoContent />
  );
};

export default Logo;
