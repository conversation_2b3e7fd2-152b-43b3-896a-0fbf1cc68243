/**
 * Leaderboard Page
 * @description Page that displays the real-time leaderboard
 * @version 1.0.0
 * @status stable
 */

import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import RealTimeLeaderboard from '../components/RealTimeLeaderboard';
import HistoricalLeaderboard from '../components/HistoricalLeaderboard';
import UserRankingHistory from '../components/UserRankingHistory';
import { Button } from '@/common/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/common/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/common/components/ui/card';
import { leaderboardService, LeaderboardStats } from '../services/leaderboardService';
import { useToast } from '@/common/components/ui/use-toast';
import { ArrowLeft, BarChart2, Calendar, Clock, Users } from 'lucide-react';
import { Link } from 'react-router-dom';

/**
 * Challenge summary information
 */
interface ChallengeSummary {
  id: number;
  name: string;
  type: string;
  startDate: string;
  endDate: string;
  status: string;
  participantCount: number;
  prizePool: number;
}

// Challenge statistics are already imported from leaderboardService

/**
 * Leaderboard page component
 */
const LeaderboardPage: React.FC = () => {
  const { challengeId } = useParams<{ challengeId: string }>();
  const [challenge, setChallenge] = useState<ChallengeSummary | null>(null);
  const [stats, setStats] = useState<LeaderboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Fetch challenge details and stats
  useEffect(() => {
    const fetchChallengeData = async () => {
      if (!challengeId) return;

      try {
        setLoading(true);

        // Fetch challenge details from the API
        const challengeResponse = await fetch(`/api/challenges/${challengeId}`);
        const challengeData = await challengeResponse.json();
        setChallenge(challengeData);

        // Fetch leaderboard stats using our service
        const statsData = await leaderboardService.getLeaderboardStatistics(parseInt(challengeId));
        setStats(statsData);
      } catch (error) {
        console.error('Error fetching challenge data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load challenge data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchChallengeData();
  }, [challengeId, toast]);

  // Format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back button */}
      <div className="mb-6">
        <Link to="/challenges">
          <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Challenges
          </Button>
        </Link>
      </div>

      {/* Challenge header */}
      {challenge && (
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
            {challenge.name}
          </h1>
          <div className="flex flex-wrap gap-3 text-sm text-white/70">
            <div className="flex items-center">
              <Calendar className="mr-1 h-4 w-4" />
              {formatDate(challenge.startDate)} - {formatDate(challenge.endDate)}
            </div>
            <div className="flex items-center">
              <Users className="mr-1 h-4 w-4" />
              {challenge.participantCount} Participants
            </div>
            <div className="flex items-center">
              <BarChart2 className="mr-1 h-4 w-4" />
              Prize Pool: ${challenge.prizePool.toLocaleString()}
            </div>
          </div>
        </div>
      )}

      {/* Tabs for different views */}
      <Tabs defaultValue="leaderboard" className="mb-8">
        <TabsList className="bg-[#0a1f3d] border border-[#2a4d7d]/30">
          <TabsTrigger value="leaderboard" className="data-[state=active]:bg-forex-primary/20 data-[state=active]:text-forex-primary">
            Live Leaderboard
          </TabsTrigger>
          <TabsTrigger value="history" className="data-[state=active]:bg-forex-primary/20 data-[state=active]:text-forex-primary">
            Historical Data
          </TabsTrigger>
          <TabsTrigger value="user-history" className="data-[state=active]:bg-forex-primary/20 data-[state=active]:text-forex-primary">
            Your Ranking
          </TabsTrigger>
          <TabsTrigger value="statistics" className="data-[state=active]:bg-forex-primary/20 data-[state=active]:text-forex-primary">
            Statistics
          </TabsTrigger>
        </TabsList>

        {/* Live Leaderboard Tab */}
        <TabsContent value="leaderboard" className="mt-6">
          {challengeId && (
            <RealTimeLeaderboard
              challengeId={parseInt(challengeId)}
              showRefreshButton={true}
              showLastUpdated={true}
              enablePagination={true}
              limit={20}
            />
          )}
        </TabsContent>

        {/* Historical Data Tab */}
        <TabsContent value="history" className="mt-6">
          {challengeId && (
            <HistoricalLeaderboard
              challengeId={parseInt(challengeId)}
            />
          )}
        </TabsContent>

        {/* User Ranking History Tab */}
        <TabsContent value="user-history" className="mt-6">
          {challengeId && (
            <UserRankingHistory
              challengeId={parseInt(challengeId)}
            />
          )}
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="statistics" className="mt-6">
          {stats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <Card className="bg-[#0c1b31] border-[#2a4d7d]/30">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-white/70">Participants</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalParticipants}</div>
                  <CardDescription>Active traders</CardDescription>
                </CardContent>
              </Card>

              <Card className="bg-[#0c1b31] border-[#2a4d7d]/30">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-white/70">Average Profit</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-forex-profit">
                    {stats.averageScore >= 0 ? '+' : ''}{stats.averageScore.toFixed(2)}%
                  </div>
                  <CardDescription>Across all participants</CardDescription>
                </CardContent>
              </Card>

              <Card className="bg-[#0c1b31] border-[#2a4d7d]/30">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-white/70">Top Performer</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-forex-profit">
                    +{stats.topScore.toFixed(2)}%
                  </div>
                  <CardDescription>Highest profit percentage</CardDescription>
                </CardContent>
              </Card>

              <Card className="bg-[#0c1b31] border-[#2a4d7d]/30">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-white/70">Median Profit</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {stats.medianScore >= 0 ? '+' : ''}{stats.medianScore.toFixed(2)}%
                  </div>
                  <CardDescription>Middle performer</CardDescription>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card className="bg-[#0c1b31] border-[#2a4d7d]/30">
              <CardHeader>
                <CardTitle>Statistics</CardTitle>
                <CardDescription>
                  Loading statistics or no data available...
                </CardDescription>
              </CardHeader>
            </Card>
          )}

          {/* Score Distribution */}
          {stats && stats.scoreDistribution && (
            <Card className="bg-[#0c1b31] border-[#2a4d7d]/30">
              <CardHeader>
                <CardTitle className="text-lg">Profit Distribution</CardTitle>
                <CardDescription>
                  Distribution of traders across profit ranges
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats.scoreDistribution.map((item) => (
                    <div key={item.range} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.range}</span>
                        <span className="text-sm text-white/70">{item.count} traders</span>
                      </div>
                      <div className="h-2 w-full bg-[#1a3a5f] rounded-full overflow-hidden">
                        <div
                          className="h-full bg-forex-primary rounded-full"
                          style={{
                            width: `${(item.count / stats.totalParticipants) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LeaderboardPage;
