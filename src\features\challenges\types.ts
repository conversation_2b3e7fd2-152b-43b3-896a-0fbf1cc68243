/**
 * Challenge Types
 * @description TypeScript types for challenge-related data
 * @version 1.0.0
 * @status stable
 */

/**
 * Challenge type enum
 */
export enum ChallengeType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

/**
 * Challenge status enum
 */
export enum ChallengeStatus {
  UPCOMING = 'upcoming',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * Base ruleset interface for all challenge types
 */
export interface BaseRuleset {
  initialBalance: number;
  maxDrawdownPercent: number;
  maxRiskPerTradePercent: number;
  noHedging: boolean;
  noMartingale: boolean;
  minTradeDurationMinutes: number;
  minTrades: number;
}

/**
 * Daily challenge ruleset
 */
export interface DailyRuleset extends BaseRuleset {
  // Daily-specific rules can be added here
}

/**
 * Weekly and Monthly challenge ruleset
 */
export interface ExtendedRuleset extends BaseRuleset {
  maxDailyDrawdownPercent: number;
  minSwingTradeDays?: number;
  minTradingDays?: number;
}

/**
 * Union type for all ruleset types
 */
export type ChallengeRuleset = DailyRuleset | ExtendedRuleset;

/**
 * Challenge interface
 */
export interface Challenge {
  id: number;
  type: ChallengeType;
  name: string;
  startDate: Date | string;
  endDate: Date | string;
  ruleset: ChallengeRuleset;
  prizePool: number;
  entryFee: number;
  status: ChallengeStatus;
  participantCount?: number;
}

/**
 * Challenge entry interface
 */
export interface ChallengeEntry {
  id: number;
  userId: string;
  challengeId: number;
  challenge: Challenge;
  ctraderAccountId: string;
  connectStatus: string;
  disqualified: boolean;
  disqualificationReason?: string;
  initialBalance: number;
  currentBalance?: number;
  currentEquity?: number;
  maxDrawdown?: number;
  pnlPercent?: number;
  tradeCount?: number;
  winRate?: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

/**
 * Challenge template interface
 */
export interface ChallengeTemplate {
  name: string;
  type: ChallengeType;
  duration: string;
  startDate: Date;
  endDate: Date;
  entryFee: number;
  prizePool: number;
  ruleset: ChallengeRuleset;
  description: string;
}
