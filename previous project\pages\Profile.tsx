import React, { useEffect } from "react";
import { useAccount } from "wagmi";
import { useAuthStore } from "@/features/auth/stores/authStore";
import { 
  PersonalInfoCard, 
  SecuritySettingsCard, 
  WalletInfoCard, 
  CryptoAddressCard 
} from "@/features/profile";
import { Button } from "@/common/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Profile page component for TradeChampionX
 * 
 * Displays and allows management of user profile information including:
 * - Personal information (username, email)
 * - Security settings (password, 2FA)
 * - Financial information (wallet balance, transaction history)
 * - Crypto address management
 * 
 * @status stable
 * @version 1.0.0
 */
const Profile = () => {
  const { address, isConnected } = useAccount();
  const { user, isAuthenticated } = useAuthStore();

  if (!isConnected || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Authentication Required</h2>
          <p className="text-gray-600">Please connect your wallet and sign in to view your profile.</p>
        </div>
      </div>
    );
  }

  // User is authenticated and available at this point

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-forex-primary">TradeChampionX</h1>
            <nav className="ml-8 hidden md:flex space-x-6">
              <Link to="/dashboard" className="text-forex-dark hover:text-forex-primary">Dashboard</Link>
              <Link to="/profile" className="text-forex-primary font-medium" aria-current="page">Profile</Link>
            </nav>
          </div>
          <div className="flex items-center">
            <Link to="/dashboard">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-forex-dark">Your Profile</h1>
          <p className="text-forex-neutral">Manage your account settings and preferences</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Information */}
          <PersonalInfoCard user={user} />

          {/* Security Settings */}
          <SecuritySettingsCard />

          {/* Wallet Information */}
          <WalletInfoCard user={user} />

          {/* Crypto Address */}
          <CryptoAddressCard user={user} />
        </div>
      </main>
    </div>
  );
};

export default Profile;
