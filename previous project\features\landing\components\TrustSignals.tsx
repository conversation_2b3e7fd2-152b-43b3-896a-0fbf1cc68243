
import React from 'react';
import { Shield, Clock, Badge, CheckCircle, Award, TrendingUp } from 'lucide-react';

const trustFeatures = [
  {
    title: "cTrader Verified",
    description: "Official API integration with real-time data sync and secure trade execution",
    icon: Shield,
    color: "text-blue-400",
    bgColor: "bg-blue-500/10",
    highlight: "Verified Integration"
  },
  {
    title: "24h Payout System",
    description: "Winners receive rewards next day without delays or complicated withdrawal processes",
    icon: Clock,
    color: "text-green-400",
    bgColor: "bg-green-500/10",
    highlight: "Fastest in Industry"
  },
  {
    title: "Transparent Rules",
    description: "Clear guidelines with no hidden clauses or loopholes designed to disqualify traders",
    icon: CheckCircle,
    color: "text-purple-400",
    bgColor: "bg-purple-500/10",
    highlight: "No Hidden Clauses"
  },
  {
    title: "Built By Traders",
    description: "Created by experienced traders who understand what serious competitors need to succeed",
    icon: Badge,
    color: "text-amber-400",
    bgColor: "bg-amber-500/10",
    highlight: "Expert-Designed"
  }
];

const TrustSignals = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-[#0f1c2e] via-[#1a2c4c] to-[#0f1c2e] relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-indigo-500/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <div className="relative inline-block mb-2">
              <div className="h-0.5 w-24 bg-gradient-to-r from-transparent via-blue-400 to-transparent mx-auto mb-6"></div>
              <span className="inline-block px-6 py-2 bg-[#0f1c2e] text-blue-400 font-semibold rounded-lg mb-4 border border-blue-500/30">
                TRUSTED BY SERIOUS TRADERS
              </span>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Reliability You Can Count On
            </h2>

            <p className="text-lg text-blue-100/80 max-w-2xl mx-auto">
              We're committed to fair competition, transparent operations, and a trading environment that rewards skill
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {trustFeatures.map((feature, index) => (
              <div
                key={index}
                className="relative bg-gradient-to-br from-[#1a2c4c]/90 to-[#0f1c2e]/70 backdrop-blur-sm border border-blue-400/10 rounded-xl p-6 overflow-hidden group"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                {/* Animated background effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Animated border glow on hover */}
                <div className="absolute inset-0 rounded-xl border border-blue-500/0 group-hover:border-blue-500/30 transition-all duration-500 group-hover:shadow-md"></div>

                {/* Icon with animated background */}
                <div className="relative">
                  <div className={`w-14 h-14 rounded-lg ${feature.bgColor} flex items-center justify-center mb-5 transform group-hover:scale-110 transition-all duration-500 group-hover:shadow-md`}>
                    <feature.icon className={`w-7 h-7 ${feature.color} group-hover:animate-pulse`} />
                  </div>

                  {/* Subtle glow effect */}
                  <div className="absolute -top-1 -right-1 w-10 h-10 rounded-full bg-blue-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>

                {/* Badge highlight */}
                <div className="absolute top-2 right-2 px-2 py-0.5 bg-blue-500/10 rounded-full border border-blue-500/20 text-xs text-blue-300 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  {feature.highlight}
                </div>

                {/* Title with gradient on hover */}
                <h3 className="text-xl font-bold mb-3 text-white group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-blue-400 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-500">
                  {feature.title}
                </h3>

                {/* Description with increased opacity on hover */}
                <p className="text-blue-100/70 group-hover:text-blue-100/90 transition-colors duration-500">
                  {feature.description}
                </p>

                {/* Subtle bottom border indicator on hover */}
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
              </div>
            ))}
          </div>

          <div className="mt-12 p-6 border border-blue-500/20 rounded-xl bg-[#1a2c4c]/50 backdrop-blur-sm text-center relative overflow-hidden group hover:border-blue-500/30 transition-all duration-500">
            {/* Background glow */}
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-blue-500/5 rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-indigo-500/5 rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              <div className="flex items-center space-x-3">
                <TrendingUp className="w-6 h-6 text-blue-400" />
                <p className="text-blue-100 font-medium">
                  <span className="font-bold text-white">TradeChampionX</span> is committed to creating a fair trading environment where skill is rewarded, not punished.
                </p>
              </div>

              <div className="h-8 w-px bg-blue-500/20 hidden md:block"></div>

              <div className="flex items-center space-x-3">
                <Award className="w-6 h-6 text-amber-400" />
                <p className="text-blue-100 font-medium">
                  Join a platform that puts <span className="font-bold text-white">traders first</span>, with transparent rules and guaranteed payouts.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustSignals;
