import React from 'react';
import { User, Crown, Shield, Star } from 'lucide-react';

interface UserAvatarProps {
  address?: string;
  displayName?: string;
  role?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showStatus?: boolean;
  isOnline?: boolean;
  className?: string;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  address,
  displayName,
  role = 'user',
  size = 'md',
  showStatus = false,
  isOnline = false,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 20,
    xl: 24
  };

  const getRoleConfig = () => {
    switch (role) {
      case 'admin':
        return {
          gradient: 'from-red-500 to-pink-500',
          icon: Crown,
          iconColor: 'text-yellow-300'
        };
      case 'moderator':
        return {
          gradient: 'from-purple-500 to-indigo-500',
          icon: Shield,
          iconColor: 'text-blue-300'
        };
      case 'vip':
        return {
          gradient: 'from-yellow-500 to-orange-500',
          icon: Star,
          iconColor: 'text-yellow-300'
        };
      default:
        return {
          gradient: 'from-forex-primary to-forex-secondary',
          icon: User,
          iconColor: 'text-white'
        };
    }
  };

  const config = getRoleConfig();
  const Icon = config.icon;

  // Generate initials from display name or address
  const getInitials = () => {
    if (displayName) {
      return displayName
        .split(' ')
        .map(word => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (address) {
      return address.slice(2, 4).toUpperCase();
    }
    return 'U';
  };

  return (
    <div className={`relative ${className}`}>
      <div className={`
        ${sizeClasses[size]} 
        rounded-full 
        bg-gradient-to-br ${config.gradient}
        flex items-center justify-center 
        text-white font-bold 
        shadow-lg 
        relative 
        overflow-hidden 
        group
        hover-lift
        transition-all duration-200
      `}>
        {/* Background animation effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center">
          {displayName || address ? (
            <span className={`font-bold ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-lg' : size === 'xl' ? 'text-xl' : 'text-sm'}`}>
              {getInitials()}
            </span>
          ) : (
            <Icon size={iconSizes[size]} className={config.iconColor} />
          )}
        </div>

        {/* Role indicator */}
        {role !== 'user' && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-forex-dark rounded-full flex items-center justify-center border-2 border-forex-dark">
            <Icon size={10} className={config.iconColor} />
          </div>
        )}
      </div>

      {/* Online status indicator */}
      {showStatus && (
        <div className={`
          absolute -bottom-1 -right-1 
          w-3 h-3 
          rounded-full 
          border-2 border-forex-dark
          ${isOnline ? 'bg-green-400 animate-pulse-slow' : 'bg-gray-400'}
          transition-colors duration-200
        `} />
      )}
    </div>
  );
};
