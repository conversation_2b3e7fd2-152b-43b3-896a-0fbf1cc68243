import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/common/components/ui';
import {
  Trophy,
  Shield,
  TrendingUp,
  Users,
  DollarSign,
  Zap,
  Crown,
  Target,
  ChevronRight,
  Play,
  Eye,
  Wallet,
  CheckCircle,
  ArrowRight,
  Star,
  BarChart3,
  Clock,
  Globe,
  Lock,
  Coins
} from 'lucide-react';

const App = () => {
  const [stats, setStats] = useState({
    totalPrizePool: 847250,
    activeChallenges: 23,
    totalTraders: 5847,
    hostEarnings: 125000,
    avgPayout: 2850
  });

  // Animate stats on mount
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalPrizePool: prev.totalPrizePool + Math.floor(Math.random() * 500),
        totalTraders: prev.totalTraders + Math.floor(Math.random() * 3),
        hostEarnings: prev.hostEarnings + Math.floor(Math.random() * 200)
      }));
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary text-white">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-900/20 via-accent-purple/10 to-accent-cyan/10 pointer-events-none"></div>

        {/* Floating elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 border border-primary-500/30 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-accent-emerald/30 rounded-full animate-bounce"></div>
          <div className="absolute bottom-40 left-1/4 w-16 h-16 border border-accent-pink/30 rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-1/3 w-20 h-20 border border-accent-orange/30 rounded-full animate-bounce"></div>
        </div>

        {/* Trading chart animation */}
        <div className="absolute bottom-0 left-0 right-0 h-24 opacity-20">
          <svg width="100%" height="100%" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path
              d="M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
              fill="url(#gradientChart)"
            >
              <animate
                attributeName="d"
                dur="20s"
                repeatCount="indefinite"
                values="
                  M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z;
                  M0,80 C150,120 350,60 500,100 C650,140 800,60 1000,100 C1100,130 1200,100 1200,100 L1200,200 L0,200 Z;
                  M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
              />
            </path>
            <defs>
              <linearGradient id="gradientChart" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#0284c7" />
                <stop offset="100%" stopColor="#0ea5e9" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Live Stats Bar */}
          <div className="max-w-5xl mx-auto mb-12">
            <div className="glass-card p-6 border border-glass-border backdrop-blur-md">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">
                    ${stats.totalPrizePool.toLocaleString()}
                  </div>
                  <div className="text-sm text-text-tertiary">Total Prize Pool</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-emerald mb-1">
                    {stats.activeChallenges}
                  </div>
                  <div className="text-sm text-text-tertiary">Live Challenges</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-cyan mb-1">
                    {stats.totalTraders.toLocaleString()}
                  </div>
                  <div className="text-sm text-text-tertiary">Active Traders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-orange mb-1">
                    ${stats.hostEarnings.toLocaleString()}
                  </div>
                  <div className="text-sm text-text-tertiary">Host Earnings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-accent-purple mb-1">
                    ${stats.avgPayout.toLocaleString()}
                  </div>
                  <div className="text-sm text-text-tertiary">Avg Payout</div>
                </div>
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto text-center">
            {/* Badge */}
            <div className="glass-card inline-block py-2 px-4 rounded-full mb-6 border border-primary-500/30">
              <span className="text-primary-400 text-sm font-medium flex items-center gap-2">
                <Trophy className="w-4 h-4" />
                Influencer-Hosted Trading Competitions • On-Chain Escrow • Live Payouts
              </span>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-text-primary mb-6 leading-tight">
              Trade. Compete.{" "}
              <span className="text-gradient bg-gradient-to-r from-primary-400 to-accent-purple bg-clip-text text-transparent">
                Dominate.
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-text-secondary mb-12 max-w-3xl mx-auto">
              Join influencer-hosted trading challenges with real market data.
              Compete for USDC prizes secured in on-chain escrow.
              <span className="text-accent-emerald font-semibold">No wallet? Watch live!</span>
            </p>

            {/* Multiple CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <Button
                size="lg"
                className="btn-primary text-white font-semibold text-base px-8 py-6 shadow-lg hover:scale-105 transition-all duration-300"
              >
                <Trophy className="mr-2 h-5 w-5" />
                Join Live Challenges
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="border-primary-500/50 text-primary-400 hover:bg-primary-500/10 hover:border-primary-400 font-semibold text-base px-8 py-6 hover:scale-105 transition-all duration-300"
              >
                <Crown className="mr-2 h-5 w-5" />
                Become a Host
              </Button>

              <Button
                size="lg"
                variant="ghost"
                className="text-text-secondary hover:text-text-primary hover:bg-glass-bg font-medium text-base px-6 py-6 hover:scale-105 transition-all duration-300"
              >
                <Eye className="mr-2 h-5 w-5" />
                Watch Live
              </Button>
            </div>

            {/* Key Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card-modern text-center p-6 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">On-Chain Escrow</h3>
                <p className="text-sm text-text-tertiary">
                  Entry fees locked in smart contracts. Transparent prize pools with automatic payouts.
                </p>
              </div>

              <div className="card-modern text-center p-6 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">Live Market Data</h3>
                <p className="text-sm text-text-tertiary">
                  Paper trade with real-time market data. Server-authoritative fills ensure fairness.
                </p>
              </div>

              <div className="card-modern text-center p-6 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">USDC Prizes</h3>
                <p className="text-sm text-text-tertiary">
                  Win real USDC paid directly to your wallet. No delays, no middlemen.
                </p>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-text-tertiary text-sm">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-accent-emerald" />
                <span>Base L2 Network</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-primary-400" />
                <span>Licensed Market Data</span>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-accent-cyan" />
                <span>Spectator Mode Available</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default App;
