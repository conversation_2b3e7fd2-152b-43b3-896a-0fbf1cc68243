import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAccount } from 'wagmi';
import { useAuthStore } from '../stores/authStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  adminOnly = false
}) => {
  const { isConnected } = useAccount();
  const { user, isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    // Show loading state while authentication loads
    return (
      <div className="min-h-screen flex items-center justify-center bg-forex-dark">
        <div className="w-12 h-12 border-4 border-forex-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!isConnected || !isAuthenticated) {
    // Redirect to home if not connected or authenticated
    return <Navigate to="/" replace />;
  }

  // If this is an admin-only route, check if the user is an admin
  if (adminOnly) {
    const isAdmin = user?.role === 'admin';

    if (!isAdmin) {
      // Redirect to home if not an admin
      return <Navigate to="/" replace />;
    }
  }

  // If all checks pass, render the children
  return <>{children}</>;
};

export default ProtectedRoute;
