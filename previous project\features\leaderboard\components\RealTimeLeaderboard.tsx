/**
 * Real-Time Leaderboard Component
 * @description Component that displays a real-time updating leaderboard
 * @version 1.0.0
 * @status stable
 */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectLeaderboard,
  selectLeaderboardLoading,
  selectLeaderboardError,
  selectLeaderboardLastUpdated,
  selectLeaderboardIsRealtime,
  fetchLeaderboard
} from '../redux/leaderboardSlice';
import { RootState } from '@/app/store';
import { DetailedLeaderboardEntry } from '@/types/socketTypes';
import {
  TrendingUp,
  TrendingDown,
  Medal,
  Clock,
  RefreshCw,
  ChevronRight,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  Info
} from 'lucide-react';
import {
  Button,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Badge
} from '@/common/components/ui';
import LeaderboardListener from './LeaderboardListener';
import { formatDistanceToNow } from 'date-fns';

// Create a simple Skeleton component since it's not exported from UI
const Skeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={`animate-pulse ${className}`}></div>
);

/**
 * RealTimeLeaderboard component props
 */
interface RealTimeLeaderboardProps {
  challengeId: number;
  limit?: number;
  showRefreshButton?: boolean;
  showLastUpdated?: boolean;
  enablePagination?: boolean;
  className?: string;
}

/**
 * Component that displays a real-time updating leaderboard
 */
const RealTimeLeaderboard: React.FC<RealTimeLeaderboardProps> = ({
  challengeId,
  limit = 10,
  showRefreshButton = true,
  showLastUpdated = true,
  enablePagination = false,
  className = '',
}) => {
  const dispatch = useDispatch();
  const leaderboard = useSelector((state: RootState) => selectLeaderboard(state, challengeId));
  const loading = useSelector(selectLeaderboardLoading);
  const error = useSelector(selectLeaderboardError);
  const lastUpdated = useSelector((state: RootState) => selectLeaderboardLastUpdated(state, challengeId));
  const isRealtime = useSelector((state: RootState) => selectLeaderboardIsRealtime(state, challengeId));

  const [highlightedRow, setHighlightedRow] = useState<number | null>(null);
  const [animatedRows, setAnimatedRows] = useState<Record<number, boolean>>({});
  const [expandedView, setExpandedView] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(limit);
  const [previousLeaderboard, setPreviousLeaderboard] = useState<DetailedLeaderboardEntry[]>([]);
  const [rankChanges, setRankChanges] = useState<Record<string, number>>({});

  // Fetch leaderboard data on component mount
  useEffect(() => {
    dispatch(fetchLeaderboard(challengeId) as any);
  }, [dispatch, challengeId]);

  // Handle manual refresh
  const handleRefresh = () => {
    dispatch(fetchLeaderboard(challengeId) as any);
  };

  // Format last updated time
  const formattedLastUpdated = lastUpdated
    ? formatDistanceToNow(new Date(lastUpdated), { addSuffix: true })
    : 'Never';

  // Track previous leaderboard state to calculate changes
  useEffect(() => {
    if (leaderboard.length > 0) {
      // Calculate rank and score changes
      const newRankChanges: Record<string, number> = {};

      leaderboard.forEach((entry, index) => {
        const prevIndex = previousLeaderboard.findIndex(prev => prev.userId === entry.userId);

        if (prevIndex !== -1) {
          // Calculate rank change (positive means improved rank)
          newRankChanges[entry.userId] = prevIndex - index;
        }
      });

      // Create a record of rows to animate
      const newAnimatedRows: Record<number, boolean> = {};
      leaderboard.forEach((entry, index) => {
        // Animate if rank changed
        if (newRankChanges[entry.userId] && newRankChanges[entry.userId] !== 0) {
          newAnimatedRows[index] = true;
        }
      });

      setRankChanges(newRankChanges);
      setAnimatedRows(newAnimatedRows);

      // Clear animations after 2 seconds
      const timer = setTimeout(() => {
        setAnimatedRows({});
      }, 2000);

      // Update previous leaderboard for next comparison
      setPreviousLeaderboard(leaderboard);

      return () => clearTimeout(timer);
    }
  }, [leaderboard, previousLeaderboard]);

  // Handle pagination
  const totalPages = enablePagination ? Math.ceil(leaderboard.length / itemsPerPage) : 1;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Determine which entries to display based on pagination and expanded view
  const displayEntries = expandedView
    ? leaderboard
    : enablePagination
      ? leaderboard.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
      : leaderboard.slice(0, limit);

  // If loading, show skeleton
  if (loading && leaderboard.length === 0) {
    return (
      <div className={`bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden ${className}`}>
        <div className="p-4 border-b border-[#2a4d7d]/30">
          <Skeleton className="h-6 w-48 bg-[#1a3a5f]" />
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-[#0a1f3d] text-left border-b border-[#2a4d7d]/30">
                <th className="px-5 py-3"><Skeleton className="h-4 w-10 bg-[#1a3a5f]" /></th>
                <th className="px-5 py-3"><Skeleton className="h-4 w-24 bg-[#1a3a5f]" /></th>
                <th className="px-5 py-3 text-right"><Skeleton className="h-4 w-16 bg-[#1a3a5f] ml-auto" /></th>
                <th className="px-5 py-3 text-right"><Skeleton className="h-4 w-12 bg-[#1a3a5f] ml-auto" /></th>
                <th className="px-5 py-3 text-right"><Skeleton className="h-4 w-16 bg-[#1a3a5f] ml-auto" /></th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }).map((_, index) => (
                <tr key={index} className="border-b border-[#2a4d7d]/20">
                  <td className="px-5 py-4"><Skeleton className="h-4 w-8 bg-[#1a3a5f]" /></td>
                  <td className="px-5 py-4"><Skeleton className="h-4 w-32 bg-[#1a3a5f]" /></td>
                  <td className="px-5 py-4 text-right"><Skeleton className="h-4 w-16 bg-[#1a3a5f] ml-auto" /></td>
                  <td className="px-5 py-4 text-right"><Skeleton className="h-4 w-10 bg-[#1a3a5f] ml-auto" /></td>
                  <td className="px-5 py-4 text-right"><Skeleton className="h-4 w-16 bg-[#1a3a5f] ml-auto" /></td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className={`bg-[#0c1b31] rounded-lg shadow-lg p-6 text-center ${className}`}>
        <p className="text-forex-loss mb-4">Error loading leaderboard: {error}</p>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={`bg-[#0c1b31] rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Include the LeaderboardListener component */}
      <LeaderboardListener challengeId={challengeId} />

      {/* Header */}
      <div className="p-4 border-b border-[#2a4d7d]/30 flex justify-between items-center">
        <h3 className="text-lg font-bold text-white flex items-center">
          Challenge #{challengeId} Leaderboard
          {isRealtime && (
            <Badge variant="outline" className="ml-2 bg-forex-primary/20 text-forex-primary border-forex-primary/30 text-xs">
              <Clock className="mr-1 h-3 w-3" />
              LIVE
            </Badge>
          )}
        </h3>

        {showRefreshButton && (
          <Button
            onClick={handleRefresh}
            variant="ghost"
            size="sm"
            className="text-forex-primary hover:text-white hover:bg-forex-primary/20"
          >
            <RefreshCw className="mr-1 h-4 w-4" />
            Refresh
          </Button>
        )}
      </div>

      {/* Leaderboard Table */}
      <div className="overflow-x-auto md:overflow-x-hidden relative">
        <table className="w-full min-w-[650px] md:min-w-0">
          <thead>
            <tr className="bg-[#0a1f3d] text-left border-b border-[#2a4d7d]/30">
              <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider w-[60px] md:w-auto">Rank</th>
              <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider">Name</th>
              <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Profit (%)</th>
              <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right hidden md:table-cell">Trades</th>
              <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Change</th>
            </tr>
          </thead>
          <tbody>
            {displayEntries.map((entry: DetailedLeaderboardEntry, index: number) => (
              <tr
                key={entry.userId}
                className={`transition-all duration-300 ${
                  animatedRows[index] ? "bg-forex-primary/10" :
                  highlightedRow === index ? "bg-[#1a3a5f]" :
                  index === 0
                    ? "bg-gradient-to-r from-amber-500/10 to-transparent"
                    : index === 1
                      ? "bg-gradient-to-r from-slate-400/10 to-transparent"
                      : index === 2
                        ? "bg-gradient-to-r from-amber-700/10 to-transparent"
                        : "bg-[#0c1b31]/40"
                } hover:bg-[#1a3a5f] hover:scale-[1.01] hover:shadow-md border-b border-[#2a4d7d]/20`}
                onMouseEnter={() => setHighlightedRow(index)}
                onMouseLeave={() => setHighlightedRow(null)}
              >
                <td className="px-3 md:px-5 py-3">
                  <div className="flex items-center">
                    {index === 0 && <Medal className="h-4 w-4 text-amber-500 mr-1.5" />}
                    {index === 1 && <Medal className="h-4 w-4 text-slate-400 mr-1.5" />}
                    {index === 2 && <Medal className="h-4 w-4 text-amber-700 mr-1.5" />}
                    <span className={`font-bold ${index < 3 ? 'text-white' : 'text-white/80'}`}>
                      {index + 1}
                    </span>
                  </div>
                </td>
                <td className="px-3 md:px-5 py-3">
                  <div className="flex items-center">
                    <span className="font-medium text-white">{entry.username}</span>
                    {entry.badges && entry.badges.length > 0 && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="ml-2 flex">
                              {entry.badges.map((badge, badgeIndex) => (
                                <div
                                  key={badgeIndex}
                                  className="h-5 w-5 rounded-full bg-forex-primary/20 flex items-center justify-center ml-1"
                                >
                                  <span className="text-xs text-forex-primary">{badge.icon}</span>
                                </div>
                              ))}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="space-y-1">
                              {entry.badges.map((badge, badgeIndex) => (
                                <div key={badgeIndex} className="flex items-center">
                                  <span className="text-xs mr-1">{badge.icon}</span>
                                  <span className="text-xs font-medium">{badge.name}</span>
                                  <span className="text-xs text-white/70 ml-1">- {badge.description}</span>
                                </div>
                              ))}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </td>
                <td className="px-3 md:px-5 py-3 text-right font-bold text-forex-profit">
                  <div className={`inline-flex items-center justify-end ${animatedRows[index] ? 'animate-pulse' : ''}`}>
                    {entry.score >= 0 ? '+' : ''}{entry.score.toFixed(2)}%
                  </div>
                </td>
                <td className="px-3 md:px-5 py-3 text-right hidden md:table-cell">
                  <span className="text-white/80 font-medium">{entry.tradeCount}</span>
                </td>
                <td className="px-3 md:px-5 py-3 text-right">
                  <div className={`flex items-center justify-end ${
                    rankChanges[entry.userId] > 0 ? "text-forex-profit" :
                    rankChanges[entry.userId] < 0 ? "text-forex-loss" : "text-white/50"
                  }`}>
                    {rankChanges[entry.userId] > 0 ? (
                      <>
                        <TrendingUp className={`w-3 h-3 md:w-3.5 md:h-3.5 mr-1 md:mr-1.5 ${animatedRows[index] ? 'animate-bounce-soft' : ''}`} />
                        +{rankChanges[entry.userId]}
                      </>
                    ) : rankChanges[entry.userId] < 0 ? (
                      <>
                        <TrendingDown className={`w-3 h-3 md:w-3.5 md:h-3.5 mr-1 md:mr-1.5 ${animatedRows[index] ? 'animate-bounce-soft' : ''}`} />
                        {rankChanges[entry.userId]}
                      </>
                    ) : entry.change ? (
                      <>
                        {entry.change > 0 ? (
                          <>
                            <TrendingUp className="w-3 h-3 md:w-3.5 md:h-3.5 mr-1 md:mr-1.5" />
                            +{entry.change}
                          </>
                        ) : entry.change < 0 ? (
                          <>
                            <TrendingDown className="w-3 h-3 md:w-3.5 md:h-3.5 mr-1 md:mr-1.5" />
                            {entry.change}
                          </>
                        ) : '-'
                        }
                      </>
                    ) : (
                      '-'
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="bg-[#0a1f3d] px-3 md:px-5 py-3 flex flex-col sm:flex-row justify-between items-center gap-2">
        <div className="flex items-center">
          {showLastUpdated && (
            <div className="flex items-center text-xs md:text-sm text-white/70">
              <Clock className="h-3 w-3 mr-1" />
              <span>Updated: {formattedLastUpdated}</span>

              {isRealtime && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3 w-3 ml-1 text-forex-primary cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">This leaderboard updates in real-time as trades are closed</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Pagination Controls */}
          {enablePagination && totalPages > 1 && (
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <span className="text-xs text-white/70 mx-2">
                Page {currentPage} of {totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Show All / Show Less Buttons */}
          {!enablePagination && leaderboard.length > limit && !expandedView && (
            <Button
              variant="ghost"
              size="sm"
              className="text-forex-primary hover:text-white hover:bg-forex-primary/20 group px-2 md:px-3 py-1.5 text-xs md:text-sm"
              onClick={() => setExpandedView(true)}
            >
              Show All ({leaderboard.length})
              <ChevronDown className="ml-1 md:ml-1.5 h-3 w-3 md:h-4 md:w-4 group-hover:translate-y-1 transition-transform" />
            </Button>
          )}

          {!enablePagination && expandedView && (
            <Button
              variant="ghost"
              size="sm"
              className="text-forex-primary hover:text-white hover:bg-forex-primary/20 group px-2 md:px-3 py-1.5 text-xs md:text-sm"
              onClick={() => setExpandedView(false)}
            >
              Show Less
              <ChevronUp className="ml-1 md:ml-1.5 h-3 w-3 md:h-4 md:w-4 group-hover:-translate-y-1 transition-transform" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default RealTimeLeaderboard;
