@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 217.2 32.6% 17.5%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
}

/* Custom utility classes */
@layer utilities {
  .glass-card {
    @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-accent-purple bg-clip-text text-transparent;
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 hover:from-primary-700 hover:to-primary-600;
  }
  
  .card-modern {
    @apply glass-card p-6 hover:bg-white/10 transition-all duration-300;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
}

/* Dark theme backgrounds */
.bg-dark-primary { background: #0a0a0a; }
.bg-dark-secondary { background: #111111; }
.bg-dark-tertiary { background: #1a1a1a; }

/* Text colors */
.text-primary { color: #ffffff; }
.text-secondary { color: rgba(255, 255, 255, 0.8); }
.text-tertiary { color: rgba(255, 255, 255, 0.6); }
