import React, { useState, useEffect } from 'react';
import {
  Shield,
  Clock,
  Badge,
  CheckCircle,
  Award,
  TrendingUp,
  HeartHandshake,
  AlertCircle,
  ChevronRight,
  BarChart3,
  Zap,
  Users
} from 'lucide-react';

// Combined trust pillars from both sections
const trustPillars = [
  {
    title: "Verified Platform",
    description: "Official cTrader API integration with real-time data sync and secure trade execution",
    icon: Shield,
    color: "text-blue-400",
    bgColor: "bg-blue-500/10",
    highlight: "Secure & Verified",
    category: "trust"
  },
  {
    title: "Next-Day Payouts",
    description: "Winners receive rewards within 24 hours - no delays or complicated withdrawal processes",
    icon: Clock,
    color: "text-green-400",
    bgColor: "bg-green-500/10",
    highlight: "Fastest in Industry",
    category: "advantage"
  },
  {
    title: "Transparent Rules",
    description: "Clear guidelines with no hidden clauses or loopholes designed to disqualify traders",
    icon: CheckCircle,
    color: "text-purple-400",
    bgColor: "bg-purple-500/10",
    highlight: "No Hidden Clauses",
    category: "trust"
  },
  {
    title: "Real Withdrawals",
    description: "No withdrawal delays or impossible hurdles. When you win, you get paid. Period.",
    icon: HeartHandshake,
    color: "text-amber-400",
    bgColor: "bg-amber-500/10",
    highlight: "Guaranteed Payouts",
    category: "advantage"
  },
  {
    title: "Fair Competition",
    description: "Tired of price manipulation and widened spreads? Our platform ensures fair trading conditions",
    icon: AlertCircle,
    color: "text-red-400",
    bgColor: "bg-red-500/10",
    highlight: "No Manipulation",
    category: "trust"
  },
  {
    title: "Community Rewards",
    description: "75% of entry fees go directly to top performers. The bigger our community, the bigger your rewards",
    icon: Award,
    color: "text-blue-400",
    bgColor: "bg-blue-500/10",
    highlight: "Community-First",
    category: "advantage"
  },
  {
    title: "Daily & Weekly Challenges",
    description: "No more waiting months for evaluation. Compete in short-term challenges and get paid quickly",
    icon: Zap,
    color: "text-yellow-400",
    bgColor: "bg-yellow-500/10",
    highlight: "Rapid Challenges",
    category: "advantage"
  },
  {
    title: "Built By Traders",
    description: "Created by experienced traders who understand what serious competitors need to succeed",
    icon: Users,
    color: "text-indigo-400",
    bgColor: "bg-indigo-500/10",
    highlight: "Expert-Designed",
    category: "trust"
  }
];

const comparisonPoints = [
  {
    propFirm: "Long Evaluations (30-60 days)",
    tradechampionx: "Instant Daily/Weekly Challenges",
    highlight: true
  },
  {
    propFirm: "Delayed Payouts (2-3 months)",
    tradechampionx: "Next-Day Payouts",
    highlight: false
  },
  {
    propFirm: "Focus on Capital",
    tradechampionx: "Focus on Skill",
    highlight: true
  },
  {
    propFirm: "Hidden Fees & Subscriptions",
    tradechampionx: "Transparent One-Time Payments",
    highlight: false
  },
  {
    propFirm: "Profit When You Fail",
    tradechampionx: "75% to Community Pool",
    highlight: true
  }
];

const TrustPillars = () => {
  const [activeTab, setActiveTab] = useState('trust');

  // Filter pillars based on active tab
  const filteredPillars = trustPillars.filter(pillar => pillar.category === activeTab);

  // Refresh AOS animations when tab changes
  useEffect(() => {
    // Small timeout to ensure DOM is updated before refreshing animations
    const timer = setTimeout(() => {
      if (typeof window !== 'undefined' && window.AOS) {
        window.AOS.refresh();
      }
    }, 10);

    return () => clearTimeout(timer);
  }, [activeTab]);

  return (
    <section className="py-20 bg-gradient-to-br from-[#0f1c2e] via-[#1a2c4c] to-[#0f1c2e] relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-indigo-500/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <div className="relative inline-block mb-2">
              <div className="h-0.5 w-24 bg-gradient-to-r from-transparent via-blue-400 to-transparent mx-auto mb-6"></div>
              <span className="inline-block px-6 py-2 bg-[#0f1c2e] text-blue-400 font-semibold rounded-lg mb-4 border border-blue-500/30">
                WHY TRADERS TRUST US
              </span>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Why Serious Traders Choose Us
            </h2>

            <p className="text-lg text-blue-100/80 max-w-2xl mx-auto mb-8">
              A platform built by traders who understand what you need: reliability you can trust and advantages you won't find elsewhere
            </p>

            {/* Tab navigation */}
            <div className="inline-flex bg-[#1a2c4c]/50 backdrop-blur-sm rounded-lg p-1 border border-blue-500/20 mb-8">
              <button
                onClick={() => {
                  setActiveTab('trust');
                  // Force immediate animation
                  setTimeout(() => {
                    document.querySelectorAll('[data-aos]').forEach(el => {
                      el.classList.add('aos-animate');
                    });
                  }, 50);
                }}
                className={`px-5 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === 'trust'
                    ? 'bg-purple-500 text-white shadow-md'
                    : 'text-blue-100/70 hover:text-blue-100'
                }`}
              >
                Trust & Security
              </button>
              <button
                onClick={() => {
                  setActiveTab('advantage');
                  // Force immediate animation
                  setTimeout(() => {
                    document.querySelectorAll('[data-aos]').forEach(el => {
                      el.classList.add('aos-animate');
                    });
                  }, 50);
                }}
                className={`px-5 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === 'advantage'
                    ? 'bg-green-500 text-white shadow-md'
                    : 'text-blue-100/70 hover:text-blue-100'
                }`}
              >
                Our Advantages
              </button>
            </div>
          </div>

          {/* Trust Pillars Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {filteredPillars.map((pillar, index) => (
              <div
                key={index}
                className={`relative bg-gradient-to-br ${
                  pillar.category === 'trust'
                    ? 'from-[#1a2c4c]/90 to-[#0f1c2e]/70 border-purple-400/10'
                    : 'from-[#1a2c4c]/90 to-[#0f1c2e]/70 border-green-400/10'
                } backdrop-blur-sm border rounded-xl p-6 overflow-hidden group`}
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                {/* Animated background effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${
                  pillar.category === 'trust'
                    ? 'from-purple-500/5 to-transparent'
                    : 'from-green-500/5 to-transparent'
                } opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>

                {/* Animated border glow on hover */}
                <div className={`absolute inset-0 rounded-xl border ${
                  pillar.category === 'trust'
                    ? 'border-purple-500/0 group-hover:border-purple-500/30'
                    : 'border-green-500/0 group-hover:border-green-500/30'
                } transition-all duration-500 group-hover:shadow-md`}></div>

                {/* Icon with animated background */}
                <div className="relative">
                  <div className={`w-14 h-14 rounded-lg ${pillar.bgColor} flex items-center justify-center mb-5 transform group-hover:scale-110 transition-all duration-500 group-hover:shadow-md`}>
                    <pillar.icon className={`w-7 h-7 ${pillar.color} group-hover:animate-pulse`} />
                  </div>

                  {/* Subtle glow effect */}
                  <div className={`absolute -top-1 -right-1 w-10 h-10 rounded-full ${
                    pillar.category === 'trust'
                      ? 'bg-purple-500/20'
                      : 'bg-green-500/20'
                  } blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
                </div>

                {/* Badge highlight */}
                <div className={`absolute top-2 right-2 px-2 py-0.5 ${
                  pillar.category === 'trust'
                    ? 'bg-purple-500/10 border-purple-500/20 text-purple-300'
                    : 'bg-green-500/10 border-green-500/20 text-green-300'
                } rounded-full border text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-500`}>
                  {pillar.highlight}
                </div>

                {/* Category indicator - now a small top border */}
                <div className={`absolute top-0 left-0 right-0 h-1 ${
                  pillar.category === 'trust'
                    ? 'bg-purple-500/50'
                    : 'bg-green-500/50'
                } rounded-t-xl`}></div>

                {/* Title with gradient on hover */}
                <h3 className={`text-xl font-bold mb-3 text-white group-hover:bg-gradient-to-r ${
                  pillar.category === 'trust'
                    ? 'group-hover:from-white group-hover:to-purple-300'
                    : 'group-hover:from-white group-hover:to-green-300'
                } group-hover:bg-clip-text group-hover:text-transparent transition-all duration-500`}>
                  {pillar.title}
                </h3>

                {/* Description with increased opacity on hover */}
                <p className="text-blue-100/70 group-hover:text-blue-100/90 transition-colors duration-500">
                  {pillar.description}
                </p>

                {/* Subtle bottom border indicator on hover */}
                <div className={`absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r ${
                  pillar.category === 'trust'
                    ? 'from-purple-500/0 via-purple-500/50 to-purple-500/0'
                    : 'from-green-500/0 via-green-500/50 to-green-500/0'
                } opacity-0 group-hover:opacity-100 transition-all duration-500`}></div>
              </div>
            ))}
          </div>

          {/* Revenue Distribution Comparison */}
          <div className="mt-16 max-w-3xl mx-auto">
            <div className="text-center mb-8">
              <span className="inline-block px-4 py-1 bg-blue-500/20 text-blue-400 font-medium rounded-full mb-4 border border-blue-500/30">
                Community-First Model
              </span>
              <h3 className="text-2xl md:text-3xl font-bold text-blue-100 mb-4">
                Where Your Money Goes
              </h3>
              <p className="text-blue-100/80">
                Unlike prop firms that profit when you fail, we share 75% of all entry fees with our community
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* TradeChampionX Model */}
              <div className="bg-[#1a2c4c]/80 backdrop-blur-sm border border-blue-400/10 rounded-xl p-6 overflow-hidden relative transform transition-transform hover:scale-105">
                <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 rounded-bl-lg text-sm font-medium">
                  Our Model
                </div>
                <h4 className="text-xl font-bold text-blue-100 mb-4">TradeChampionX</h4>

                <div className="flex items-center justify-center mb-6">
                  <div className="relative w-40 h-40">
                    <div className="absolute inset-0 rounded-full border-8 border-blue-500/30"></div>
                    <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-blue-500 animate-spin-slow"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div>
                        <div className="text-3xl font-bold text-blue-400">75%</div>
                        <div className="text-sm text-blue-100/70">To Community</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100/70">Top Performers</span>
                    <span className="text-blue-400 font-medium">60%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100/70">Wallet Credits (Top 30%)</span>
                    <span className="text-blue-400 font-medium">15%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100/70">Platform Operations</span>
                    <span className="text-blue-100/70 font-medium">25%</span>
                  </div>
                </div>
              </div>

              {/* Traditional Prop Firm Model */}
              <div className="bg-[#1a2c4c]/80 backdrop-blur-sm border border-blue-400/10 rounded-xl p-6 overflow-hidden relative transform transition-transform hover:scale-105">
                <div className="absolute top-0 right-0 bg-red-500 text-white px-3 py-1 rounded-bl-lg text-sm font-medium">
                  Their Model
                </div>
                <h4 className="text-xl font-bold text-blue-100 mb-4">Traditional Prop Firms</h4>

                <div className="flex items-center justify-center mb-6">
                  <div className="relative w-40 h-40">
                    <div className="absolute inset-0 rounded-full border-8 border-red-500/30"></div>
                    <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-red-500 animate-spin-slow"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div>
                        <div className="text-3xl font-bold text-red-400">80%+</div>
                        <div className="text-sm text-blue-100/70">To Company</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100/70">Profit from Failures</span>
                    <span className="text-red-400 font-medium">High</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100/70">Disqualification Rate</span>
                    <span className="text-red-400 font-medium">70-90%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100/70">To Traders</span>
                    <span className="text-blue-100/70 font-medium">~20%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Comparison Table */}
          <div className="mt-16 max-w-3xl mx-auto">
            <div className="text-center mb-8">
              <span className="inline-block px-4 py-1 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 text-blue-400 font-medium rounded-full mb-3 border border-blue-500/30">
                The TradeChampionX Difference
              </span>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-3 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                How We Compare
              </h3>
            </div>

            {/* Compact, Modern Comparison with Wow Factor */}
            <div className="relative bg-[#1a2c4c]/60 backdrop-blur-sm border border-blue-400/10 rounded-xl overflow-hidden">
              {/* Animated background glow */}
              <div className="absolute -top-20 -right-20 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-indigo-500/10 rounded-full blur-3xl"></div>

              {/* Header */}
              <div className="grid grid-cols-3 border-b border-blue-400/10">
                <div className="p-3 text-center"></div>
                <div className="p-3 text-center border-x border-blue-400/10 bg-red-500/5">
                  <span className="text-red-400 font-medium text-sm">Traditional Firms</span>
                </div>
                <div className="p-3 text-center bg-blue-500/5">
                  <span className="text-blue-400 font-medium text-sm">TradeChampionX</span>
                </div>
              </div>

              {/* Comparison rows with hover effects */}
              {comparisonPoints.map((point, index) => (
                <div key={index} className={`grid grid-cols-3 border-b border-blue-400/10 group hover:bg-blue-500/5 transition-colors duration-300 ${index === comparisonPoints.length - 1 ? 'border-b-0' : ''}`}>
                  <div className="p-3 flex items-center">
                    <div className={`w-1 h-8 ${point.highlight ? 'bg-blue-500' : 'bg-blue-500/30'} rounded-full mr-2`}></div>
                    <span className="text-white font-medium text-sm">{point.propFirm.split('(')[0]}</span>
                  </div>
                  <div className="p-3 text-center border-x border-blue-400/10 bg-red-500/5 group-hover:bg-red-500/10 transition-colors duration-300">
                    <div className="flex items-center justify-center space-x-1">
                      <span className="text-red-400 text-sm">{point.propFirm}</span>
                      <span className="text-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">✗</span>
                    </div>
                  </div>
                  <div className="p-3 text-center bg-blue-500/5 group-hover:bg-blue-500/10 transition-colors duration-300">
                    <div className="flex items-center justify-center space-x-1">
                      <span className="text-blue-400 font-medium text-sm">{point.tradechampionx}</span>
                      <span className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">✓</span>
                    </div>
                  </div>
                </div>
              ))}

              {/* Animated highlight bar that follows mouse on hover (visual flair) */}
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0 opacity-0 group-hover:opacity-100"></div>
            </div>

            <div className="mt-6 flex justify-center">
              <div className="inline-flex items-center px-4 py-2 bg-[#1a2c4c]/80 backdrop-blur-sm border border-blue-500/20 rounded-lg shadow-sm hover:shadow-blue-500/10 transition-all">
                <span className="text-blue-100 font-bold text-sm bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">#NoMoreShadyPropFirms</span>
              </div>
            </div>
          </div>


        </div>
      </div>
    </section>
  );
};

export default TrustPillars;
