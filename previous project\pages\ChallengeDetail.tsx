import { useParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Button } from '@/common/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { Badge } from '@/common/components/ui/badge';
import { Separator } from '@/common/components/ui/separator';
import { Header } from '@/common/components/layout/Header';
import { mockChallenges, mockLeaderboardEntries } from '@/lib/mockData';
import { formatCurrency, formatDateTime } from '@/common/utils/formatters';
import { Trophy, Users, Clock, DollarSign, Target, TrendingUp } from 'lucide-react';

const ChallengeDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [challenge, setChallenge] = useState<any>(null);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadChallenge = async () => {
      setLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const challengeData = mockChallenges.find(c => c.id === parseInt(id || '0'));
      if (challengeData) {
        setChallenge(challengeData);
        setLeaderboard(mockLeaderboardEntries);
      }
      
      setLoading(false);
    };

    loadChallenge();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-white text-lg">Loading challenge...</div>
          </div>
        </div>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-white">
            <h1 className="text-2xl font-bold mb-4">Challenge Not Found</h1>
            <p className="text-gray-400">The challenge you're looking for doesn't exist.</p>
          </div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-500';
      case 'upcoming': return 'bg-blue-500';
      case 'completed': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Challenge Header */}
        <div className="mb-8">
          <div className="relative rounded-xl overflow-hidden mb-6">
            <img 
              src={challenge.bannerUrl} 
              alt={challenge.name}
              className="w-full h-48 object-cover"
            />
            <div className="absolute inset-0 bg-black/50 flex items-end">
              <div className="p-6 text-white">
                <Badge className={`${getStatusColor(challenge.status)} text-white mb-2`}>
                  {challenge.status.toUpperCase()}
                </Badge>
                <h1 className="text-3xl font-bold mb-2">{challenge.name}</h1>
                <p className="text-lg opacity-90">Hosted by {challenge.hostName}</p>
              </div>
            </div>
          </div>

          {/* Host Message */}
          {challenge.hostMessage && (
            <Card className="bg-gray-800/50 border-gray-700 mb-6">
              <CardContent className="p-4">
                <p className="text-forex-primary font-medium">"{challenge.hostMessage}"</p>
                <p className="text-gray-400 text-sm mt-2">- {challenge.hostName}</p>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Challenge Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardContent className="p-4 text-center">
                  <DollarSign className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white">{formatCurrency(challenge.prizePool)}</div>
                  <div className="text-sm text-gray-400">Prize Pool</div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardContent className="p-4 text-center">
                  <Users className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white">{challenge.participantCount}</div>
                  <div className="text-sm text-gray-400">Participants</div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardContent className="p-4 text-center">
                  <Target className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white">{formatCurrency(challenge.entryFee)}</div>
                  <div className="text-sm text-gray-400">Entry Fee</div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardContent className="p-4 text-center">
                  <Clock className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white">{challenge.type}</div>
                  <div className="text-sm text-gray-400">Duration</div>
                </CardContent>
              </Card>
            </div>

            {/* Challenge Description */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">About This Challenge</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">{challenge.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Start Date:</span>
                    <span className="text-white ml-2">{formatDateTime(challenge.startDate)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">End Date:</span>
                    <span className="text-white ml-2">{formatDateTime(challenge.endDate)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Mock Trading Interface Placeholder */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Trading Interface
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-900/50 rounded-lg p-8 text-center">
                  <p className="text-gray-400 mb-4">TradingView Chart & Order Interface</p>
                  <p className="text-sm text-gray-500">This will be integrated with live market data and paper trading engine</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Join Challenge */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Join Challenge</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full bg-forex-primary hover:bg-forex-primary/90">
                  Connect Wallet & Join
                </Button>
                <p className="text-xs text-gray-400 text-center">
                  Entry fee: {formatCurrency(challenge.entryFee)} USDC
                </p>
              </CardContent>
            </Card>

            {/* Live Leaderboard */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Trophy className="h-5 w-5 mr-2" />
                  Live Leaderboard
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {leaderboard.slice(0, 5).map((entry, index) => (
                    <div key={entry.userId} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                          index === 0 ? 'bg-yellow-500 text-black' :
                          index === 1 ? 'bg-gray-400 text-black' :
                          index === 2 ? 'bg-orange-600 text-white' :
                          'bg-gray-600 text-white'
                        }`}>
                          {entry.rank}
                        </div>
                        <div>
                          <div className="text-white text-sm font-medium">{entry.username}</div>
                          <div className="text-gray-400 text-xs">
                            {entry.badges?.join(' ')} {formatCurrency(entry.equity)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-bold ${entry.score >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {entry.score >= 0 ? '+' : ''}{entry.score.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <Separator className="my-4 bg-gray-700" />
                <Button variant="outline" className="w-full text-forex-primary border-forex-primary hover:bg-forex-primary/10">
                  View Full Leaderboard
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChallengeDetail;
