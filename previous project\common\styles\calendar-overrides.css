/**
 * Calendar Overrides
 * Custom styles for react-day-picker to work better with dark theme
 */

/* Calendar container */
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: #0284c7; /* forex-primary */
  --rdp-background-color: #1e293b; /* forex-card */
  --rdp-accent-color-dark: #0369a1; /* forex-hover */
  --rdp-background-color-dark: #0f172a; /* forex-dark */
  --rdp-outline: 2px solid var(--rdp-accent-color);
  --rdp-outline-selected: 2px solid var(--rdp-accent-color);
  margin: 1rem;
  color: white;
}

/* Calendar table */
.rdp-table {
  width: 100%;
  border-collapse: collapse;
}

/* Calendar day */
.rdp-day {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rdp-day:hover:not(.rdp-day_disabled) {
  background-color: rgba(3, 105, 161, 0.5); /* forex-hover with opacity */
  color: white;
}

/* Selected day */
.rdp-day_selected {
  background-color: #0284c7 !important; /* forex-primary */
  color: white !important;
}

.rdp-day_selected:hover {
  background-color: #0369a1 !important; /* forex-hover */
  color: white !important;
}

/* Today */
.rdp-day_today {
  background-color: rgba(15, 23, 42, 0.7) !important; /* forex-dark with opacity */
  color: white !important;
  font-weight: bold;
}

/* Disabled days */
.rdp-day_disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Month navigation buttons */
.rdp-nav_button {
  width: 32px;
  height: 32px;
  color: white !important;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rdp-nav_button:hover {
  background-color: rgba(3, 105, 161, 0.2); /* forex-hover with opacity */
  border-radius: 4px;
}

/* Month caption */
.rdp-caption {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  color: white;
}

/* Month caption label */
.rdp-caption_label {
  font-weight: 600;
  color: white;
  font-size: 1rem;
  padding: 0 1rem;
}

/* Head cells (weekday names) */
.rdp-head_cell {
  color: #64748b; /* forex-neutral */
  font-weight: 500;
  padding: 0.5rem 0;
  text-align: center;
}

/* Outside days (days from other months) */
.rdp-day_outside {
  opacity: 0.5;
}

/* Fix for z-index issues */
.rdp-months {
  z-index: 50;
  position: relative;
}

/* Fix for popover content */
[data-radix-popper-content-wrapper] {
  z-index: 999 !important;
}

/* Prevent calendar from closing on click */
.rdp-button {
  pointer-events: auto !important;
}

/* Ensure calendar buttons are clickable */
.rdp-nav_button,
.rdp-day,
.rdp-button {
  cursor: pointer !important;
  user-select: none !important;
}

/* Improve button visibility */
.rdp-nav_button:focus,
.rdp-day:focus,
.rdp-button:focus {
  outline: 2px solid #0284c7 !important;
  outline-offset: 2px !important;
}

/* Ensure popover stays open */
[data-state="open"] {
  pointer-events: auto !important;
}
