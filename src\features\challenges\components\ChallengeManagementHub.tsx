/**
 * ChallengeManagementHub component for TradeChampionX
 *
 * A comprehensive dashboard for managing active challenges, with status indicators,
 * leaderboard integration, and compliance monitoring.
 *
 * @version 1.0.0
 * @status experimental
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Badge,
  Progress,
  Skeleton,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Separator
} from '@/common/components/ui';
import {
  Trophy,
  ChevronRight,
  Clock,
  AlertTriangle,
  CheckCircle,
  Timer,
  Percent,
  TrendingUp,
  LineChart,
  BarChart3,
  Users,
  ArrowUpRight,
  ShieldAlert,
  LayoutDashboard
} from 'lucide-react';
import { useUserChallenges } from '../hooks/useUserChallenges';
import { motion } from 'framer-motion';
import { formatCurrency, formatDate, formatNumber } from '@/common/utils/formatters';

// Challenge status badge component
const ChallengeStatusBadge = ({ status }: { status: string }) => {
  const getStatusProps = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return {
          className: 'bg-green-500/20 text-green-300 border-green-500/30',
          icon: <CheckCircle className="h-3 w-3 mr-1" />
        };
      case 'at risk':
        return {
          className: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
          icon: <AlertTriangle className="h-3 w-3 mr-1" />
        };
      case 'disqualified':
        return {
          className: 'bg-red-500/20 text-red-300 border-red-500/30',
          icon: <ShieldAlert className="h-3 w-3 mr-1" />
        };
      case 'completed':
        return {
          className: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
          icon: <Trophy className="h-3 w-3 mr-1" />
        };
      default:
        return {
          className: 'bg-gray-500/20 text-gray-300 border-gray-500/30',
          icon: <Clock className="h-3 w-3 mr-1" />
        };
    }
  };

  const { className, icon } = getStatusProps(status);

  return (
    <Badge variant="outline" className={`flex items-center ${className}`}>
      {icon}
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

// Challenge type badge component
const ChallengeTypeBadge = ({ type }: { type: string }) => {
  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'daily':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'weekly':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'monthly':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  return (
    <Badge
      variant="outline"
      className={`${getTypeColor(type)}`}
    >
      {type.charAt(0).toUpperCase() + type.slice(1)}
    </Badge>
  );
};

// Challenge card component
const ChallengeCard = ({ challenge, index }: { challenge: any, index: number }) => {
  const navigate = useNavigate();

  // Calculate time remaining
  const now = new Date();
  const endDate = new Date(challenge.challenge.endDate);
  const totalSeconds = (endDate.getTime() - now.getTime()) / 1000;
  const days = Math.floor(totalSeconds / 86400);
  const hours = Math.floor((totalSeconds % 86400) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);

  // Calculate progress percentage
  const startDate = new Date(challenge.challenge.startDate);
  const totalDuration = (endDate.getTime() - startDate.getTime());
  const elapsed = (now.getTime() - startDate.getTime());
  const progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

  // Use real data with fallbacks for missing values
  const pnlPercentage = challenge.metrics?.pnlPercent || 0;
  const isPnlPositive = pnlPercentage >= 0;

  // Use real rank data with fallbacks
  const rank = challenge.metrics?.rank || 0;
  const totalParticipants = challenge.challenge._count?.challengeEntries || 0;

  // Use real compliance data with fallbacks
  const maxDrawdown = challenge.metrics?.maxDrawdown || 0;
  const maxDrawdownLimit = 5; // This should come from challenge rules
  const isCompliant = maxDrawdown <= maxDrawdownLimit;
  const complianceStatus = isCompliant ? 'active' : 'at risk';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm overflow-hidden hover:border-blue-500/40 transition-all duration-300">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-2">
              <ChallengeTypeBadge type={challenge.challenge.type} />
              <ChallengeStatusBadge status={complianceStatus} />
            </div>
            <Badge variant="outline" className="bg-blue-500/10 border-blue-500/20 flex items-center gap-1">
              <Trophy className="h-3 w-3" />
              <span>#{rank} of {totalParticipants}</span>
            </Badge>
          </div>
          <CardTitle className="text-lg font-medium mt-2">
            {challenge.challenge.type} Challenge #{challenge.challenge.id}
          </CardTitle>
          <CardDescription>
            Started {formatDate(challenge.challenge.startDate)}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* Time remaining */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3 mb-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Clock className="h-4 w-4 text-blue-300 mr-2" />
                <span className="text-sm font-medium text-blue-300">Time Remaining</span>
              </div>
              <span className="text-xs text-white font-medium">{Math.round(progressPercentage)}% elapsed</span>
            </div>

            <Progress value={progressPercentage} className="h-1.5 bg-blue-900/50 mb-3" />

            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="bg-blue-500/10 rounded-md p-1.5">
                <div className="text-md font-mono font-bold text-white">
                  {days}
                </div>
                <div className="text-[10px] text-blue-300">DAYS</div>
              </div>
              <div className="bg-blue-500/10 rounded-md p-1.5">
                <div className="text-md font-mono font-bold text-white">
                  {hours}
                </div>
                <div className="text-[10px] text-blue-300">HOURS</div>
              </div>
              <div className="bg-blue-500/10 rounded-md p-1.5">
                <div className="text-md font-mono font-bold text-white">
                  {minutes}
                </div>
                <div className="text-[10px] text-blue-300">MINUTES</div>
              </div>
            </div>
          </div>

          {/* Performance metrics */}
          <div className="grid grid-cols-2 gap-3 mb-3">
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-2.5">
              <div className="flex items-center mb-1">
                <TrendingUp className="h-4 w-4 text-blue-300 mr-1.5" />
                <span className="text-xs font-medium text-blue-300">Profit & Loss</span>
              </div>
              <div className={`text-lg font-bold ${isPnlPositive ? 'text-green-400' : 'text-red-400'}`}>
                {isPnlPositive ? '+' : ''}{pnlPercentage}%
              </div>
            </div>

            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-2.5">
              <div className="flex items-center mb-1">
                <Percent className="h-4 w-4 text-blue-300 mr-1.5" />
                <span className="text-xs font-medium text-blue-300">Max Drawdown</span>
              </div>
              <div className={`text-lg font-bold ${maxDrawdown > maxDrawdownLimit * 0.8 ? 'text-amber-400' : 'text-white'}`}>
                {maxDrawdown}%
              </div>
              <div className="text-[10px] text-gray-400">Limit: {maxDrawdownLimit}%</div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-blue-300 border-blue-500/30 hover:bg-blue-500/10"
              onClick={() => navigate(`/challenge-entries/${challenge.id}`)}
            >
              <LayoutDashboard className="h-4 w-4 mr-1.5" />
              Dashboard
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-blue-300 border-blue-500/30 hover:bg-blue-500/10"
              onClick={() => navigate(`/challenge-entries/${challenge.id}/trades`)}
            >
              <BarChart3 className="h-4 w-4 mr-1.5" />
              Trades
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Challenge leaderboard preview component
const ChallengeLeaderboardPreview = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [topTraders, setTopTraders] = useState([]);

  // Fetch top performers from API
  useEffect(() => {
    const fetchTopPerformers = async () => {
      try {
        setIsLoading(true);

        // TODO: Replace with real API call to get top performers
        // const response = await apiService.getTopPerformers();
        // setTopTraders(response.data || []);

        // For now, set empty array until API is implemented
        setTopTraders([]);
      } catch (error) {
        console.error('Error fetching top performers:', error);
        // Set empty array on error
        setTopTraders([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTopPerformers();
  }, []);

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Top Performers</CardTitle>
        <CardDescription>Current leaderboard for active challenges</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center p-2">
                <Skeleton className="h-6 w-6 rounded-full" />
                <Skeleton className="h-4 w-32 ml-3" />
                <Skeleton className="h-4 w-16 ml-auto" />
              </div>
            ))}
          </div>
        ) : topTraders.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-sm text-gray-400">No leaderboard data available</p>
          </div>
        ) : (
          <div className="space-y-1">
            {topTraders.map((trader, index) => (
              <div key={trader.id} className={`flex items-center p-2 rounded-md ${index === 0 ? 'bg-blue-500/10 border border-blue-500/30' : 'hover:bg-blue-500/5'}`}>
                <div className="w-6 text-center">
                  {index === 0 ? (
                    <Trophy className="h-4 w-4 text-amber-400" />
                  ) : (
                    <span className="text-sm text-gray-400">{index + 1}</span>
                  )}
                </div>
                <Avatar className="h-6 w-6 mx-2">
                  <AvatarFallback className="bg-blue-500/20 text-blue-300 text-xs">
                    {trader.name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm text-gray-300">{trader.name}</span>
                <div className="ml-auto text-green-400 text-sm font-medium">
                  +{trader.pnl.toFixed(2)}%
                </div>
              </div>
            ))}
          </div>
        )}
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-3 text-blue-300 border-blue-500/30 hover:bg-blue-500/10"
          onClick={() => navigate('/dashboard/leaderboard')}
        >
          View Full Leaderboard
          <ChevronRight className="h-4 w-4 ml-1.5" />
        </Button>
      </CardContent>
    </Card>
  );
};

// New challenge card component
const NewChallengeCard = () => {
  // Get the current location to determine if we're in the dashboard or standalone page
  const navigate = useNavigate();

  // Function to handle browse challenges click
  const handleBrowseChallenges = () => {
    // Check if we're in the dashboard challenges page
    if (window.location.pathname.includes('/dashboard/challenges')) {
      // If we're in the dashboard, just switch to the browser tab
      // This requires coordination with the parent component
      const tabsElement = document.querySelector('[role="tablist"]');
      const browserTab = tabsElement?.querySelector('[value="browser"]');

      if (browserTab) {
        // Programmatically click the browser tab
        (browserTab as HTMLElement).click();
      } else {
        // Fallback to navigation if we can't find the tab
        navigate('/dashboard/challenges?tab=browser');
      }
    } else {
      // If we're not in the dashboard, navigate to the dashboard challenges page with browser tab
      navigate('/dashboard/challenges?tab=browser');
    }
  };

  return (
    <Card className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm h-full">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Ready for a New Challenge?</CardTitle>
        <CardDescription>Browse and join upcoming trading competitions</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col h-full">
        <div className="flex-1 flex flex-col items-center justify-center py-6 space-y-4">
          <div className="bg-blue-500/10 p-4 rounded-full">
            <Trophy className="h-10 w-10 text-blue-300" />
          </div>
          <p className="text-center text-gray-400 text-sm max-w-xs">
            Join a challenge, compete with other traders, and win prizes based on your performance.
          </p>
        </div>
        <Button
          className="w-full bg-blue-600 hover:bg-blue-700"
          onClick={handleBrowseChallenges}
        >
          Browse Challenges
          <ArrowUpRight className="h-4 w-4 ml-1.5" />
        </Button>
      </CardContent>
    </Card>
  );
};

// Challenge management hub main component
const ChallengeManagementHub: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('active');
  const { data: challengeEntries, isLoading, hasActiveChallenges } = useUserChallenges();

  // Function to handle browse challenges click
  const handleBrowseChallenges = () => {
    // Check if we're in the dashboard challenges page
    if (window.location.pathname.includes('/dashboard/challenges')) {
      // If we're in the dashboard, just switch to the browser tab
      const tabsElement = document.querySelector('[role="tablist"]');
      const browserTab = tabsElement?.querySelector('[value="browser"]');

      if (browserTab) {
        // Programmatically click the browser tab
        (browserTab as HTMLElement).click();
      } else {
        // Fallback to navigation if we can't find the tab
        navigate('/dashboard/challenges?tab=browser');
      }
    } else {
      // If we're not in the dashboard, navigate to the dashboard challenges page with browser tab
      navigate('/dashboard/challenges?tab=browser');
    }
  };

  // Filter challenge entries based on active tab
  const getFilteredEntries = () => {
    if (!challengeEntries) return [];

    if (activeTab === 'active') {
      return challengeEntries.filter(entry =>
        entry.challenge.status === 'active' && !entry.disqualified
      );
    } else if (activeTab === 'completed') {
      return challengeEntries.filter(entry =>
        entry.challenge.status === 'completed' || entry.disqualified
      );
    }

    return challengeEntries;
  };

  const filteredEntries = getFilteredEntries();

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-blue-600">
            Your Challenges
          </span>
        </h2>
      </div>

      <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="active">
            Active Challenges
            {hasActiveChallenges && (
              <Badge className="ml-2 bg-blue-500/20 text-blue-300 border border-blue-500/30">
                {challengeEntries?.filter(e => e.challenge.status === 'active' && !e.disqualified).length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="completed">
            Past Challenges
          </TabsTrigger>
          <TabsTrigger value="all">All</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4 mt-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[1, 2].map((i) => (
                <Card key={i} className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
                  <CardHeader>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-24 w-full" />
                    <div className="grid grid-cols-2 gap-3">
                      <Skeleton className="h-16 w-full" />
                      <Skeleton className="h-16 w-full" />
                    </div>
                    <Skeleton className="h-9 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredEntries.length === 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <NewChallengeCard />
              <ChallengeLeaderboardPreview />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredEntries.map((entry, index) => (
                <ChallengeCard key={entry.id} challenge={entry} index={index} />
              ))}
              {filteredEntries.length < 2 && (
                <div className="md:col-span-1">
                  <NewChallengeCard />
                </div>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4 mt-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[1, 2].map((i) => (
                <Card key={i} className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
                  <CardHeader>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-16 w-full" />
                    <div className="grid grid-cols-2 gap-3">
                      <Skeleton className="h-16 w-full" />
                      <Skeleton className="h-16 w-full" />
                    </div>
                    <Skeleton className="h-9 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredEntries.length === 0 ? (
            <div className="text-center py-12">
              <Trophy className="h-12 w-12 text-blue-500/30 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">No Past Challenges</h3>
              <p className="text-gray-400 max-w-md mx-auto">
                You haven't completed any challenges yet. Start competing today to earn prizes and improve your trading skills.
              </p>
              <Button
                className="mt-4 bg-blue-600 hover:bg-blue-700"
                onClick={handleBrowseChallenges}
              >
                Browse Challenges
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredEntries.map((entry, index) => (
                <ChallengeCard key={entry.id} challenge={entry} index={index} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="all" className="space-y-4 mt-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[1, 2].map((i) => (
                <Card key={i} className="border-blue-500/20 bg-[#0f1c2e]/60 backdrop-blur-sm">
                  <CardHeader>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-16 w-full" />
                    <div className="grid grid-cols-2 gap-3">
                      <Skeleton className="h-16 w-full" />
                      <Skeleton className="h-16 w-full" />
                    </div>
                    <Skeleton className="h-9 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredEntries.length === 0 ? (
            <div className="text-center py-12">
              <Trophy className="h-12 w-12 text-blue-500/30 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-white mb-2">No Challenge History</h3>
              <p className="text-gray-400 max-w-md mx-auto">
                You haven't participated in any challenges yet. Start competing today to earn prizes and improve your trading skills.
              </p>
              <Button
                className="mt-4 bg-blue-600 hover:bg-blue-700"
                onClick={handleBrowseChallenges}
              >
                Browse Challenges
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredEntries.map((entry, index) => (
                <ChallengeCard key={entry.id} challenge={entry} index={index} />
              ))}
              <div className="md:col-span-1">
                <NewChallengeCard />
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChallengeManagementHub;